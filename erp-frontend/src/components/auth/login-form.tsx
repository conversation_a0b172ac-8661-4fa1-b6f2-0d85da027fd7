"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Building2, Eye, EyeOff, LogIn, User, Lock } from "lucide-react"

interface LoginFormProps {
  onLogin: (email: string, password: string) => Promise<boolean>
}

export function LoginForm({ onLogin }: LoginFormProps) {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      const success = await onLogin(email, password)
      if (success) {
        router.push("/dashboard")
      } else {
        setError("البريد الإلكتروني أو كلمة المرور غير صحيحة")
      }
    } catch (error) {
      setError("حدث خطأ أثناء تسجيل الدخول")
    } finally {
      setIsLoading(false)
    }
  }

  const demoAccounts = [
    { email: "<EMAIL>", password: "admin123", role: "مدير النظام", color: "bg-red-500", bgColor: "bg-red-50", textColor: "text-red-700", borderColor: "border-red-200" },
    { email: "<EMAIL>", password: "manager123", role: "مدير المبيعات", color: "bg-blue-500", bgColor: "bg-blue-50", textColor: "text-blue-700", borderColor: "border-blue-200" },
    { email: "<EMAIL>", password: "employee123", role: "موظفة مبيعات", color: "bg-green-500", bgColor: "bg-green-50", textColor: "text-green-700", borderColor: "border-green-200" },
    { email: "<EMAIL>", password: "warehouse123", role: "مسؤول المخزون", color: "bg-yellow-500", bgColor: "bg-yellow-50", textColor: "text-yellow-700", borderColor: "border-yellow-200" },
    { email: "<EMAIL>", password: "viewer123", role: "مراجعة", color: "bg-purple-500", bgColor: "bg-purple-50", textColor: "text-purple-700", borderColor: "border-purple-200" },
    { email: "<EMAIL>", password: "accountant123", role: "محاسب", color: "bg-orange-500", bgColor: "bg-orange-50", textColor: "text-orange-700", borderColor: "border-orange-200" },
  ]

  const fillDemoAccount = (demoEmail: string, demoPassword: string) => {
    setEmail(demoEmail)
    setPassword(demoPassword)
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4 gradient-bg">
      <div className="w-full max-w-6xl grid md:grid-cols-2 gap-8">
        {/* نموذج تسجيل الدخول */}
        <Card className="w-full max-w-md mx-auto auth-card custom-shadow-lg">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-primary rounded-full">
                <Building2 className="h-8 w-8 text-primary-foreground" />
              </div>
            </div>
            <CardTitle className="text-2xl font-bold">تسجيل الدخول</CardTitle>
            <CardDescription>
              ادخل بياناتك للوصول إلى نظام ERP
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">البريد الإلكتروني</label>
                <div className="relative">
                  <User className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="pr-10"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">كلمة المرور</label>
                <div className="relative">
                  <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="••••••••"
                    className="pr-10 pl-10"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2"
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <Eye className="h-4 w-4 text-muted-foreground" />
                    )}
                  </button>
                </div>
              </div>

              {error && (
                <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
                  {error}
                </div>
              )}

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                    جاري تسجيل الدخول...
                  </div>
                ) : (
                  <div className="flex items-center">
                    <LogIn className="ml-2 h-4 w-4" />
                    تسجيل الدخول
                  </div>
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* حسابات التجربة */}
        <Card className="w-full auth-card custom-shadow-lg">
          <CardHeader>
            <CardTitle className="text-xl font-bold">حسابات التجربة</CardTitle>
            <CardDescription>
              اختر أي حساب للتجربة - كل حساب له صلاحيات مختلفة
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {demoAccounts.map((account, index) => (
                <div
                  key={index}
                  className={`auth-demo-account flex items-center justify-between p-4 rounded-lg border smooth-transition ${account.bgColor} ${account.borderColor}`}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 rounded-full ${account.color} shadow-sm`}></div>
                    <div>
                      <div className={`font-medium ${account.textColor}`}>{account.role}</div>
                      <div className="text-sm text-muted-foreground">
                        {account.email}
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => fillDemoAccount(account.email, account.password)}
                    className={`${account.borderColor} ${account.textColor} hover:${account.bgColor}`}
                  >
                    استخدام
                  </Button>
                </div>
              ))}
            </div>

            <div className="mt-6 p-4 bg-muted/50 rounded-lg border">
              <h4 className="font-medium text-foreground mb-2">معلومات الصلاحيات:</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li><strong className="text-foreground">مدير النظام:</strong> صلاحيات كاملة لجميع الأقسام</li>
                <li><strong className="text-foreground">مدير المبيعات:</strong> إدارة العملاء والمبيعات</li>
                <li><strong className="text-foreground">موظفة مبيعات:</strong> إضافة وتعديل العملاء والطلبات</li>
                <li><strong className="text-foreground">مسؤول المخزون:</strong> إدارة المنتجات والمخزون</li>
                <li><strong className="text-foreground">مراجعة:</strong> عرض البيانات فقط</li>
                <li><strong className="text-foreground">محاسب:</strong> التقارير المالية والمبيعات</li>
              </ul>
            </div>

            <div className="mt-4 p-4 bg-primary/5 rounded-lg border border-primary/20">
              <h4 className="font-medium text-primary mb-2">روابط سريعة:</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <a href="/login" className="text-primary hover:underline">
                  🔐 صفحة تسجيل الدخول
                </a>
                <a href="/dashboard" className="text-primary hover:underline">
                  📊 لوحة التحكم
                </a>
                <a href="/customers" className="text-primary hover:underline">
                  👥 إدارة العملاء
                </a>
                <a href="/inventory" className="text-primary hover:underline">
                  📦 إدارة المخزون
                </a>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
