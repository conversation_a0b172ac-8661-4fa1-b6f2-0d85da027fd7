"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/components/layout/sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/sidebar.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Calendar,FileText,LayoutDashboard,MessageSquare,Package,Settings,ShoppingCart,Truck,UserCheck,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Calendar,FileText,LayoutDashboard,MessageSquare,Package,Settings,ShoppingCart,Truck,UserCheck,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Calendar,FileText,LayoutDashboard,MessageSquare,Package,Settings,ShoppingCart,Truck,UserCheck,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Calendar,FileText,LayoutDashboard,MessageSquare,Package,Settings,ShoppingCart,Truck,UserCheck,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Calendar,FileText,LayoutDashboard,MessageSquare,Package,Settings,ShoppingCart,Truck,UserCheck,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Calendar,FileText,LayoutDashboard,MessageSquare,Package,Settings,ShoppingCart,Truck,UserCheck,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Calendar,FileText,LayoutDashboard,MessageSquare,Package,Settings,ShoppingCart,Truck,UserCheck,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Calendar,FileText,LayoutDashboard,MessageSquare,Package,Settings,ShoppingCart,Truck,UserCheck,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Calendar,FileText,LayoutDashboard,MessageSquare,Package,Settings,ShoppingCart,Truck,UserCheck,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Calendar,FileText,LayoutDashboard,MessageSquare,Package,Settings,ShoppingCart,Truck,UserCheck,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Calendar,FileText,LayoutDashboard,MessageSquare,Package,Settings,ShoppingCart,Truck,UserCheck,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Calendar,FileText,LayoutDashboard,MessageSquare,Package,Settings,ShoppingCart,Truck,UserCheck,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,Calendar,FileText,LayoutDashboard,MessageSquare,Package,Settings,ShoppingCart,Truck,UserCheck,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst sidebarItems = [\n    {\n        title: \"لوحة التحكم\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        title: \"إدارة العملاء\",\n        href: \"/customers\",\n        icon: _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        title: \"إدارة المخزون\",\n        href: \"/inventory\",\n        icon: _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        title: \"المبيعات\",\n        href: \"/sales\",\n        icon: _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        title: \"المشتريات\",\n        href: \"/purchases\",\n        icon: _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        title: \"الفواتير\",\n        href: \"/invoices\",\n        icon: _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        title: \"الحسابات\",\n        href: \"/accounts\",\n        icon: _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        title: \"الموظفين\",\n        href: \"/employees\",\n        icon: _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        title: \"المواعيد\",\n        href: \"/appointments\",\n        icon: _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    {\n        title: \"الرسائل\",\n        href: \"/messages\",\n        icon: _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    },\n    {\n        title: \"التقارير\",\n        href: \"/reports\",\n        icon: _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    },\n    {\n        title: \"الشركة\",\n        href: \"/company\",\n        icon: _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    },\n    {\n        title: \"الإعدادات\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    }\n];\nfunction Sidebar(param) {\n    let { className } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"pb-12 min-h-screen\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4 py-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-3 py-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_Calendar_FileText_LayoutDashboard_MessageSquare_Package_Settings_ShoppingCart_Truck_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"h-8 w-8 ml-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/layout/sidebar.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-semibold\",\n                                children: \"نظام ERP\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/layout/sidebar.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/layout/sidebar.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: sidebarItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: pathname === item.href ? \"secondary\" : \"ghost\",\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-full justify-start\", pathname === item.href && \"bg-muted font-medium\"),\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.href,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"ml-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/layout/sidebar.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 19\n                                        }, this),\n                                        item.title\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/layout/sidebar.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 17\n                                }, this)\n                            }, item.href, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/layout/sidebar.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/layout/sidebar.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/layout/sidebar.tsx\",\n                lineNumber: 104,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/layout/sidebar.tsx\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/layout/sidebar.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"xbyQPtUVMO7MNj7WjJlpdWqRcTo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/sidebar.tsx\n"));

/***/ })

});