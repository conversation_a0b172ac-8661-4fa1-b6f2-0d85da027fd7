"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./src/components/auth/login-form.tsx":
/*!********************************************!*\
  !*** ./src/components/auth/login-form.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoginForm: () => (/* binding */ LoginForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* __next_internal_client_entry_do_not_use__ LoginForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction LoginForm(param) {\n    let { onLogin } = param;\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            const success = await onLogin(email, password);\n            if (success) {\n                router.push(\"/dashboard\");\n            } else {\n                setError(\"البريد الإلكتروني أو كلمة المرور غير صحيحة\");\n            }\n        } catch (error) {\n            setError(\"حدث خطأ أثناء تسجيل الدخول\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const demoAccounts = [\n        {\n            email: \"<EMAIL>\",\n            password: \"admin123\",\n            role: \"مدير النظام\",\n            color: \"bg-red-500\",\n            bgColor: \"bg-red-50\",\n            textColor: \"text-red-700\",\n            borderColor: \"border-red-200\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"manager123\",\n            role: \"مدير المبيعات\",\n            color: \"bg-blue-500\",\n            bgColor: \"bg-blue-50\",\n            textColor: \"text-blue-700\",\n            borderColor: \"border-blue-200\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"employee123\",\n            role: \"موظفة مبيعات\",\n            color: \"bg-green-500\",\n            bgColor: \"bg-green-50\",\n            textColor: \"text-green-700\",\n            borderColor: \"border-green-200\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"warehouse123\",\n            role: \"مسؤول المخزون\",\n            color: \"bg-yellow-500\",\n            bgColor: \"bg-yellow-50\",\n            textColor: \"text-yellow-700\",\n            borderColor: \"border-yellow-200\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"viewer123\",\n            role: \"مراجعة\",\n            color: \"bg-purple-500\",\n            bgColor: \"bg-purple-50\",\n            textColor: \"text-purple-700\",\n            borderColor: \"border-purple-200\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"accountant123\",\n            role: \"محاسب\",\n            color: \"bg-orange-500\",\n            bgColor: \"bg-orange-50\",\n            textColor: \"text-orange-700\",\n            borderColor: \"border-orange-200\"\n        }\n    ];\n    const fillDemoAccount = (demoEmail, demoPassword)=>{\n        setEmail(demoEmail);\n        setPassword(demoPassword);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-background p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-6xl grid md:grid-cols-2 gap-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"w-full max-w-md mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-primary rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-8 w-8 text-primary-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-2xl font-bold\",\n                                    children: \"تسجيل الدخول\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"ادخل بياناتك للوصول إلى نظام ERP\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"البريد الإلكتروني\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        type: \"email\",\n                                                        value: email,\n                                                        onChange: (e)=>setEmail(e.target.value),\n                                                        placeholder: \"<EMAIL>\",\n                                                        className: \"pr-10\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"كلمة المرور\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        type: showPassword ? \"text\" : \"password\",\n                                                        value: password,\n                                                        onChange: (e)=>setPassword(e.target.value),\n                                                        placeholder: \"••••••••\",\n                                                        className: \"pr-10 pl-10\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setShowPassword(!showPassword),\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2\",\n                                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-red-600 bg-red-50 p-3 rounded-md\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"submit\",\n                                        className: \"w-full\",\n                                        disabled: isLoading,\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"جاري تسجيل الدخول...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"ml-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"تسجيل الدخول\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-xl font-bold\",\n                                    children: \"حسابات التجربة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"اختر أي حساب للتجربة - كل حساب له صلاحيات مختلفة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: demoAccounts.map((account, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 rounded-lg border transition-all duration-200 hover:shadow-md \".concat(account.bgColor, \" \").concat(account.borderColor),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-4 rounded-full \".concat(account.color, \" shadow-sm\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium \".concat(account.textColor),\n                                                                    children: account.role\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                                    lineNumber: 155,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: account.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>fillDemoAccount(account.email, account.password),\n                                                    className: \"\".concat(account.borderColor, \" \").concat(account.textColor, \" hover:\").concat(account.bgColor),\n                                                    children: \"استخدام\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 p-4 bg-blue-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-blue-900 mb-2\",\n                                            children: \"معلومات الصلاحيات:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-blue-800 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"مدير النظام:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" صلاحيات كاملة لجميع الأقسام\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"مدير المبيعات:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" إدارة العملاء والمبيعات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"موظفة مبيعات:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" إضافة وتعديل العملاء والطلبات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"مسؤول المخزون:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" إدارة المنتجات والمخزون\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"مراجعة:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" عرض البيانات فقط\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"محاسب:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" التقارير المالية والمبيعات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-4 bg-green-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-green-900 mb-2\",\n                                            children: \"روابط سريعة:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-2 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/login\",\n                                                    className: \"text-green-700 hover:underline\",\n                                                    children: \"\\uD83D\\uDD10 صفحة تسجيل الدخول\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/dashboard\",\n                                                    className: \"text-green-700 hover:underline\",\n                                                    children: \"\\uD83D\\uDCCA لوحة التحكم\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/customers\",\n                                                    className: \"text-green-700 hover:underline\",\n                                                    children: \"\\uD83D\\uDC65 إدارة العملاء\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/inventory\",\n                                                    className: \"text-green-700 hover:underline\",\n                                                    children: \"\\uD83D\\uDCE6 إدارة المخزون\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginForm, \"QjaKSJEVQp1wB0abSM6uGwqVdw4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = LoginForm;\nvar _c;\n$RefreshReg$(_c, \"LoginForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/login-form.tsx\n"));

/***/ })

});