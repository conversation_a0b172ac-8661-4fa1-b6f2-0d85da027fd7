{"version": 3, "file": "compat.umd.js", "sources": ["../src/util.js", "../src/hooks.js", "../src/PureComponent.js", "../src/memo.js", "../src/forwardRef.js", "../src/Children.js", "../src/suspense.js", "../src/suspense-list.js", "../../src/constants.js", "../src/portals.js", "../src/render.js", "../src/index.js"], "sourcesContent": ["/**\n * Assign properties from `props` to `obj`\n * @template O, P The obj and props types\n * @param {O} obj The object to copy properties to\n * @param {P} props The object to copy properties from\n * @returns {O & P}\n */\nexport function assign(obj, props) {\n\tfor (let i in props) obj[i] = props[i];\n\treturn /** @type {O & P} */ (obj);\n}\n\n/**\n * Check if two objects have a different shape\n * @param {object} a\n * @param {object} b\n * @returns {boolean}\n */\nexport function shallowDiffers(a, b) {\n\tfor (let i in a) if (i !== '__source' && !(i in b)) return true;\n\tfor (let i in b) if (i !== '__source' && a[i] !== b[i]) return true;\n\treturn false;\n}\n\n/**\n * Check if two values are the same value\n * @param {*} x\n * @param {*} y\n * @returns {boolean}\n */\nexport function is(x, y) {\n\treturn (x === y && (x !== 0 || 1 / x === 1 / y)) || (x !== x && y !== y);\n}\n", "import { useState, useLayoutEffect, useEffect } from 'preact/hooks';\nimport { is } from './util';\n\n/**\n * This is taken from https://github.com/facebook/react/blob/main/packages/use-sync-external-store/src/useSyncExternalStoreShimClient.js#L84\n * on a high level this cuts out the warnings, ... and attempts a smaller implementation\n * @typedef {{ _value: any; _getSnapshot: () => any }} Store\n */\nexport function useSyncExternalStore(subscribe, getSnapshot) {\n\tconst value = getSnapshot();\n\n\t/**\n\t * @typedef {{ _instance: Store }} StoreRef\n\t * @type {[StoreRef, (store: StoreRef) => void]}\n\t */\n\tconst [{ _instance }, forceUpdate] = useState({\n\t\t_instance: { _value: value, _getSnapshot: getSnapshot }\n\t});\n\n\tuseLayoutEffect(() => {\n\t\t_instance._value = value;\n\t\t_instance._getSnapshot = getSnapshot;\n\n\t\tif (didSnapshotChange(_instance)) {\n\t\t\tforceUpdate({ _instance });\n\t\t}\n\t}, [subscribe, value, getSnapshot]);\n\n\tuseEffect(() => {\n\t\tif (didSnapshotChange(_instance)) {\n\t\t\tforceUpdate({ _instance });\n\t\t}\n\n\t\treturn subscribe(() => {\n\t\t\tif (didSnapshotChange(_instance)) {\n\t\t\t\tforceUpdate({ _instance });\n\t\t\t}\n\t\t});\n\t}, [subscribe]);\n\n\treturn value;\n}\n\n/** @type {(inst: Store) => boolean} */\nfunction didSnapshotChange(inst) {\n\tconst latestGetSnapshot = inst._getSnapshot;\n\tconst prevValue = inst._value;\n\ttry {\n\t\tconst nextValue = latestGetSnapshot();\n\t\treturn !is(prevValue, nextValue);\n\t} catch (error) {\n\t\treturn true;\n\t}\n}\n\nexport function startTransition(cb) {\n\tcb();\n}\n\nexport function useDeferredValue(val) {\n\treturn val;\n}\n\nexport function useTransition() {\n\treturn [false, startTransition];\n}\n\n// TODO: in theory this should be done after a VNode is diffed as we want to insert\n// styles/... before it attaches\nexport const useInsertionEffect = useLayoutEffect;\n", "import { Component } from 'preact';\nimport { shallowDiffers } from './util';\n\n/**\n * Component class with a predefined `shouldComponentUpdate` implementation\n */\nexport function PureComponent(p, c) {\n\tthis.props = p;\n\tthis.context = c;\n}\nPureComponent.prototype = new Component();\n// Some third-party libraries check if this property is present\nPureComponent.prototype.isPureReactComponent = true;\nPureComponent.prototype.shouldComponentUpdate = function (props, state) {\n\treturn shallowDiffers(this.props, props) || shallowDiffers(this.state, state);\n};\n", "import { createElement } from 'preact';\nimport { shallowDiffers } from './util';\n\n/**\n * Memoize a component, so that it only updates when the props actually have\n * changed. This was previously known as `React.pure`.\n * @param {import('./internal').FunctionComponent} c functional component\n * @param {(prev: object, next: object) => boolean} [comparer] Custom equality function\n * @returns {import('./internal').FunctionComponent}\n */\nexport function memo(c, comparer) {\n\tfunction shouldUpdate(nextProps) {\n\t\tlet ref = this.props.ref;\n\t\tlet updateRef = ref == nextProps.ref;\n\t\tif (!updateRef && ref) {\n\t\t\tref.call ? ref(null) : (ref.current = null);\n\t\t}\n\n\t\tif (!comparer) {\n\t\t\treturn shallowDiffers(this.props, nextProps);\n\t\t}\n\n\t\treturn !comparer(this.props, nextProps) || !updateRef;\n\t}\n\n\tfunction Memoed(props) {\n\t\tthis.shouldComponentUpdate = shouldUpdate;\n\t\treturn createElement(c, props);\n\t}\n\tMemoed.displayName = 'Memo(' + (c.displayName || c.name) + ')';\n\tMemoed.prototype.isReactComponent = true;\n\tMemoed._forwarded = true;\n\treturn Memoed;\n}\n", "import { options } from 'preact';\nimport { assign } from './util';\n\nlet oldDiffHook = options._diff;\noptions._diff = vnode => {\n\tif (vnode.type && vnode.type._forwarded && vnode.ref) {\n\t\tvnode.props.ref = vnode.ref;\n\t\tvnode.ref = null;\n\t}\n\tif (oldDiffHook) oldDiffHook(vnode);\n};\n\nexport const REACT_FORWARD_SYMBOL =\n\t(typeof Symbol != 'undefined' &&\n\t\tSymbol.for &&\n\t\tSymbol.for('react.forward_ref')) ||\n\t0xf47;\n\n/**\n * Pass ref down to a child. This is mainly used in libraries with HOCs that\n * wrap components. Using `forwardRef` there is an easy way to get a reference\n * of the wrapped component instead of one of the wrapper itself.\n * @param {import('./index').ForwardFn} fn\n * @returns {import('./internal').FunctionComponent}\n */\nexport function forwardRef(fn) {\n\tfunction Forwarded(props) {\n\t\tlet clone = assign({}, props);\n\t\tdelete clone.ref;\n\t\treturn fn(clone, props.ref || null);\n\t}\n\n\t// mobx-react checks for this being present\n\tForwarded.$$typeof = REACT_FORWARD_SYMBOL;\n\t// mobx-react heavily relies on implementation details.\n\t// It expects an object here with a `render` property,\n\t// and prototype.render will fail. Without this\n\t// mobx-react throws.\n\tForwarded.render = Forwarded;\n\n\tForwarded.prototype.isReactComponent = Forwarded._forwarded = true;\n\tForwarded.displayName = 'ForwardRef(' + (fn.displayName || fn.name) + ')';\n\treturn Forwarded;\n}\n", "import { toChildArray } from 'preact';\n\nconst mapFn = (children, fn) => {\n\tif (children == null) return null;\n\treturn toChildArray(toChildArray(children).map(fn));\n};\n\n// This API is completely unnecessary for Preact, so it's basically passthrough.\nexport const Children = {\n\tmap: mapFn,\n\tforEach: mapFn,\n\tcount(children) {\n\t\treturn children ? toChildArray(children).length : 0;\n\t},\n\tonly(children) {\n\t\tconst normalized = toChildArray(children);\n\t\tif (normalized.length !== 1) throw 'Children.only';\n\t\treturn normalized[0];\n\t},\n\ttoArray: toChildArray\n};\n", "import { Component, createElement, options, Fragment } from 'preact';\nimport { MODE_HYDRATE } from '../../src/constants';\nimport { assign } from './util';\n\nconst oldCatchError = options._catchError;\noptions._catchError = function (error, newVNode, oldVNode, errorInfo) {\n\tif (error.then) {\n\t\t/** @type {import('./internal').Component} */\n\t\tlet component;\n\t\tlet vnode = newVNode;\n\n\t\tfor (; (vnode = vnode._parent); ) {\n\t\t\tif ((component = vnode._component) && component._childDidSuspend) {\n\t\t\t\tif (newVNode._dom == null) {\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t\t}\n\t\t\t\t// Don't call oldCatchError if we found a Suspense\n\t\t\t\treturn component._childDidSuspend(error, newVNode);\n\t\t\t}\n\t\t}\n\t}\n\toldCatchError(error, newVNode, oldVNode, errorInfo);\n};\n\nconst oldUnmount = options.unmount;\noptions.unmount = function (vnode) {\n\t/** @type {import('./internal').Component} */\n\tconst component = vnode._component;\n\tif (component && component._onResolve) {\n\t\tcomponent._onResolve();\n\t}\n\n\t// if the component is still hydrating\n\t// most likely it is because the component is suspended\n\t// we set the vnode.type as `null` so that it is not a typeof function\n\t// so the unmount will remove the vnode._dom\n\tif (component && vnode._flags & MODE_HYDRATE) {\n\t\tvnode.type = null;\n\t}\n\n\tif (oldUnmount) oldUnmount(vnode);\n};\n\nfunction detachedClone(vnode, detachedParent, parentDom) {\n\tif (vnode) {\n\t\tif (vnode._component && vnode._component.__hooks) {\n\t\t\tvnode._component.__hooks._list.forEach(effect => {\n\t\t\t\tif (typeof effect._cleanup == 'function') effect._cleanup();\n\t\t\t});\n\n\t\t\tvnode._component.__hooks = null;\n\t\t}\n\n\t\tvnode = assign({}, vnode);\n\t\tif (vnode._component != null) {\n\t\t\tif (vnode._component._parentDom === parentDom) {\n\t\t\t\tvnode._component._parentDom = detachedParent;\n\t\t\t}\n\n\t\t\tvnode._component._force = true;\n\n\t\t\tvnode._component = null;\n\t\t}\n\n\t\tvnode._children =\n\t\t\tvnode._children &&\n\t\t\tvnode._children.map(child =>\n\t\t\t\tdetachedClone(child, detachedParent, parentDom)\n\t\t\t);\n\t}\n\n\treturn vnode;\n}\n\nfunction removeOriginal(vnode, detachedParent, originalParent) {\n\tif (vnode && originalParent) {\n\t\tvnode._original = null;\n\t\tvnode._children =\n\t\t\tvnode._children &&\n\t\t\tvnode._children.map(child =>\n\t\t\t\tremoveOriginal(child, detachedParent, originalParent)\n\t\t\t);\n\n\t\tif (vnode._component) {\n\t\t\tif (vnode._component._parentDom === detachedParent) {\n\t\t\t\tif (vnode._dom) {\n\t\t\t\t\toriginalParent.appendChild(vnode._dom);\n\t\t\t\t}\n\t\t\t\tvnode._component._force = true;\n\t\t\t\tvnode._component._parentDom = originalParent;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn vnode;\n}\n\n// having custom inheritance instead of a class here saves a lot of bytes\nexport function Suspense() {\n\t// we do not call super here to golf some bytes...\n\tthis._pendingSuspensionCount = 0;\n\tthis._suspenders = null;\n\tthis._detachOnNextRender = null;\n}\n\n// Things we do here to save some bytes but are not proper JS inheritance:\n// - call `new Component()` as the prototype\n// - do not set `Suspense.prototype.constructor` to `Suspense`\nSuspense.prototype = new Component();\n\n/**\n * @this {import('./internal').SuspenseComponent}\n * @param {Promise} promise The thrown promise\n * @param {import('./internal').VNode<any, any>} suspendingVNode The suspending component\n */\nSuspense.prototype._childDidSuspend = function (promise, suspendingVNode) {\n\tconst suspendingComponent = suspendingVNode._component;\n\n\t/** @type {import('./internal').SuspenseComponent} */\n\tconst c = this;\n\n\tif (c._suspenders == null) {\n\t\tc._suspenders = [];\n\t}\n\tc._suspenders.push(suspendingComponent);\n\n\tconst resolve = suspended(c._vnode);\n\n\tlet resolved = false;\n\tconst onResolved = () => {\n\t\tif (resolved) return;\n\n\t\tresolved = true;\n\t\tsuspendingComponent._onResolve = null;\n\n\t\tif (resolve) {\n\t\t\tresolve(onSuspensionComplete);\n\t\t} else {\n\t\t\tonSuspensionComplete();\n\t\t}\n\t};\n\n\tsuspendingComponent._onResolve = onResolved;\n\n\tconst onSuspensionComplete = () => {\n\t\tif (!--c._pendingSuspensionCount) {\n\t\t\t// If the suspension was during hydration we don't need to restore the\n\t\t\t// suspended children into the _children array\n\t\t\tif (c.state._suspended) {\n\t\t\t\tconst suspendedVNode = c.state._suspended;\n\t\t\t\tc._vnode._children[0] = removeOriginal(\n\t\t\t\t\tsuspendedVNode,\n\t\t\t\t\tsuspendedVNode._component._parentDom,\n\t\t\t\t\tsuspendedVNode._component._originalParentDom\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tc.setState({ _suspended: (c._detachOnNextRender = null) });\n\n\t\t\tlet suspended;\n\t\t\twhile ((suspended = c._suspenders.pop())) {\n\t\t\t\tsuspended.forceUpdate();\n\t\t\t}\n\t\t}\n\t};\n\n\t/**\n\t * We do not set `suspended: true` during hydration because we want the actual markup\n\t * to remain on screen and hydrate it when the suspense actually gets resolved.\n\t * While in non-hydration cases the usual fallback -> component flow would occour.\n\t */\n\tif (\n\t\t!c._pendingSuspensionCount++ &&\n\t\t!(suspendingVNode._flags & MODE_HYDRATE)\n\t) {\n\t\tc.setState({ _suspended: (c._detachOnNextRender = c._vnode._children[0]) });\n\t}\n\tpromise.then(onResolved, onResolved);\n};\n\nSuspense.prototype.componentWillUnmount = function () {\n\tthis._suspenders = [];\n};\n\n/**\n * @this {import('./internal').SuspenseComponent}\n * @param {import('./internal').SuspenseComponent[\"props\"]} props\n * @param {import('./internal').SuspenseState} state\n */\nSuspense.prototype.render = function (props, state) {\n\tif (this._detachOnNextRender) {\n\t\t// When the Suspense's _vnode was created by a call to createVNode\n\t\t// (i.e. due to a setState further up in the tree)\n\t\t// it's _children prop is null, in this case we \"forget\" about the parked vnodes to detach\n\t\tif (this._vnode._children) {\n\t\t\tconst detachedParent = document.createElement('div');\n\t\t\tconst detachedComponent = this._vnode._children[0]._component;\n\t\t\tthis._vnode._children[0] = detachedClone(\n\t\t\t\tthis._detachOnNextRender,\n\t\t\t\tdetachedParent,\n\t\t\t\t(detachedComponent._originalParentDom = detachedComponent._parentDom)\n\t\t\t);\n\t\t}\n\n\t\tthis._detachOnNextRender = null;\n\t}\n\n\t// Wrap fallback tree in a VNode that prevents itself from being marked as aborting mid-hydration:\n\t/** @type {import('./internal').VNode} */\n\tconst fallback =\n\t\tstate._suspended && createElement(Fragment, null, props.fallback);\n\tif (fallback) fallback._flags &= ~MODE_HYDRATE;\n\n\treturn [\n\t\tcreateElement(Fragment, null, state._suspended ? null : props.children),\n\t\tfallback\n\t];\n};\n\n/**\n * Checks and calls the parent component's _suspended method, passing in the\n * suspended vnode. This is a way for a parent (e.g. SuspenseList) to get notified\n * that one of its children/descendants suspended.\n *\n * The parent MAY return a callback. The callback will get called when the\n * suspension resolves, notifying the parent of the fact.\n * Moreover, the callback gets function `unsuspend` as a parameter. The resolved\n * child descendant will not actually get unsuspended until `unsuspend` gets called.\n * This is a way for the parent to delay unsuspending.\n *\n * If the parent does not return a callback then the resolved vnode\n * gets unsuspended immediately when it resolves.\n *\n * @param {import('./internal').VNode} vnode\n * @returns {((unsuspend: () => void) => void)?}\n */\nexport function suspended(vnode) {\n\t/** @type {import('./internal').Component} */\n\tlet component = vnode._parent._component;\n\treturn component && component._suspended && component._suspended(vnode);\n}\n\nexport function lazy(loader) {\n\tlet prom;\n\tlet component;\n\tlet error;\n\n\tfunction Lazy(props) {\n\t\tif (!prom) {\n\t\t\tprom = loader();\n\t\t\tprom.then(\n\t\t\t\texports => {\n\t\t\t\t\tcomponent = exports.default || exports;\n\t\t\t\t},\n\t\t\t\te => {\n\t\t\t\t\terror = e;\n\t\t\t\t}\n\t\t\t);\n\t\t}\n\n\t\tif (error) {\n\t\t\tthrow error;\n\t\t}\n\n\t\tif (!component) {\n\t\t\tthrow prom;\n\t\t}\n\n\t\treturn createElement(component, props);\n\t}\n\n\tLazy.displayName = 'Lazy';\n\tLazy._forwarded = true;\n\treturn Lazy;\n}\n", "import { Component, toChildArray } from 'preact';\nimport { suspended } from './suspense.js';\n\n// Indexes to linked list nodes (nodes are stored as arrays to save bytes).\nconst SUSPENDED_COUNT = 0;\nconst RESOLVED_COUNT = 1;\nconst NEXT_NODE = 2;\n\n// Having custom inheritance instead of a class here saves a lot of bytes.\nexport function SuspenseList() {\n\tthis._next = null;\n\tthis._map = null;\n}\n\n// Mark one of child's earlier suspensions as resolved.\n// Some pending callbacks may become callable due to this\n// (e.g. the last suspended descendant gets resolved when\n// revealOrder === 'together'). Process those callbacks as well.\nconst resolve = (list, child, node) => {\n\tif (++node[RESOLVED_COUNT] === node[SUSPENDED_COUNT]) {\n\t\t// The number a child (or any of its descendants) has been suspended\n\t\t// matches the number of times it's been resolved. Therefore we\n\t\t// mark the child as completely resolved by deleting it from ._map.\n\t\t// This is used to figure out when *all* children have been completely\n\t\t// resolved when revealOrder is 'together'.\n\t\tlist._map.delete(child);\n\t}\n\n\t// If revealOrder is falsy then we can do an early exit, as the\n\t// callbacks won't get queued in the node anyway.\n\t// If revealOrder is 'together' then also do an early exit\n\t// if all suspended descendants have not yet been resolved.\n\tif (\n\t\t!list.props.revealOrder ||\n\t\t(list.props.revealOrder[0] === 't' && list._map.size)\n\t) {\n\t\treturn;\n\t}\n\n\t// Walk the currently suspended children in order, calling their\n\t// stored callbacks on the way. Stop if we encounter a child that\n\t// has not been completely resolved yet.\n\tnode = list._next;\n\twhile (node) {\n\t\twhile (node.length > 3) {\n\t\t\tnode.pop()();\n\t\t}\n\t\tif (node[RESOLVED_COUNT] < node[SUSPENDED_COUNT]) {\n\t\t\tbreak;\n\t\t}\n\t\tlist._next = node = node[NEXT_NODE];\n\t}\n};\n\n// Things we do here to save some bytes but are not proper JS inheritance:\n// - call `new Component()` as the prototype\n// - do not set `Suspense.prototype.constructor` to `Suspense`\nSuspenseList.prototype = new Component();\n\nSuspenseList.prototype._suspended = function (child) {\n\tconst list = this;\n\tconst delegated = suspended(list._vnode);\n\n\tlet node = list._map.get(child);\n\tnode[SUSPENDED_COUNT]++;\n\n\treturn unsuspend => {\n\t\tconst wrappedUnsuspend = () => {\n\t\t\tif (!list.props.revealOrder) {\n\t\t\t\t// Special case the undefined (falsy) revealOrder, as there\n\t\t\t\t// is no need to coordinate a specific order or unsuspends.\n\t\t\t\tunsuspend();\n\t\t\t} else {\n\t\t\t\tnode.push(unsuspend);\n\t\t\t\tresolve(list, child, node);\n\t\t\t}\n\t\t};\n\t\tif (delegated) {\n\t\t\tdelegated(wrappedUnsuspend);\n\t\t} else {\n\t\t\twrappedUnsuspend();\n\t\t}\n\t};\n};\n\nSuspenseList.prototype.render = function (props) {\n\tthis._next = null;\n\tthis._map = new Map();\n\n\tconst children = toChildArray(props.children);\n\tif (props.revealOrder && props.revealOrder[0] === 'b') {\n\t\t// If order === 'backwards' (or, well, anything starting with a 'b')\n\t\t// then flip the child list around so that the last child will be\n\t\t// the first in the linked list.\n\t\tchildren.reverse();\n\t}\n\t// Build the linked list. Iterate through the children in reverse order\n\t// so that `_next` points to the first linked list node to be resolved.\n\tfor (let i = children.length; i--; ) {\n\t\t// Create a new linked list node as an array of form:\n\t\t// \t[suspended_count, resolved_count, next_node]\n\t\t// where suspended_count and resolved_count are numeric counters for\n\t\t// keeping track how many times a node has been suspended and resolved.\n\t\t//\n\t\t// Note that suspended_count starts from 1 instead of 0, so we can block\n\t\t// processing callbacks until componentDidMount has been called. In a sense\n\t\t// node is suspended at least until componentDidMount gets called!\n\t\t//\n\t\t// Pending callbacks are added to the end of the node:\n\t\t// \t[suspended_count, resolved_count, next_node, callback_0, callback_1, ...]\n\t\tthis._map.set(children[i], (this._next = [1, 0, this._next]));\n\t}\n\treturn props.children;\n};\n\nSuspenseList.prototype.componentDidUpdate =\n\tSuspenseList.prototype.componentDidMount = function () {\n\t\t// Iterate through all children after mounting for two reasons:\n\t\t// 1. As each node[SUSPENDED_COUNT] starts from 1, this iteration increases\n\t\t//    each node[RELEASED_COUNT] by 1, therefore balancing the counters.\n\t\t//    The nodes can now be completely consumed from the linked list.\n\t\t// 2. Handle nodes that might have gotten resolved between render and\n\t\t//    componentDidMount.\n\t\tthis._map.forEach((node, child) => {\n\t\t\tresolve(this, child, node);\n\t\t});\n\t};\n", "/** Normal hydration that attaches to a DOM tree but does not diff it. */\nexport const MODE_HYDRATE = 1 << 5;\n/** Signifies this VNode suspended on the previous render */\nexport const MODE_SUSPENDED = 1 << 7;\n/** Indicates that this node needs to be inserted while patching children */\nexport const INSERT_VNODE = 1 << 2;\n/** Indicates a VNode has been matched with another VNode in the diff */\nexport const MATCHED = 1 << 1;\n\n/** Reset all mode flags */\nexport const RESET_MODE = ~(MODE_HYDRATE | MODE_SUSPENDED);\n\nexport const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\nexport const XHTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\nexport const MATH_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n\nexport const NULL = null;\nexport const UNDEFINED = undefined;\nexport const EMPTY_OBJ = /** @type {any} */ ({});\nexport const EMPTY_ARR = [];\nexport const IS_NON_DIMENSIONAL =\n\t/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\n", "import { createElement, render } from 'preact';\n\n/**\n * @param {import('../../src/index').RenderableProps<{ context: any }>} props\n */\nfunction ContextProvider(props) {\n\tthis.getChildContext = () => props.context;\n\treturn props.children;\n}\n\n/**\n * Portal component\n * @this {import('./internal').Component}\n * @param {object | null | undefined} props\n *\n * TODO: use createRoot() instead of fake root\n */\nfunction Portal(props) {\n\tconst _this = this;\n\tlet container = props._container;\n\n\t_this.componentWillUnmount = function () {\n\t\trender(null, _this._temp);\n\t\t_this._temp = null;\n\t\t_this._container = null;\n\t};\n\n\t// When we change container we should clear our old container and\n\t// indicate a new mount.\n\tif (_this._container && _this._container !== container) {\n\t\t_this.componentWillUnmount();\n\t}\n\n\tif (!_this._temp) {\n\t\t// Ensure the element has a mask for useId invocations\n\t\tlet root = _this._vnode;\n\t\twhile (root !== null && !root._mask && root._parent !== null) {\n\t\t\troot = root._parent;\n\t\t}\n\n\t\t_this._container = container;\n\n\t\t// Create a fake DOM parent node that manages a subset of `container`'s children:\n\t\t_this._temp = {\n\t\t\tnodeType: 1,\n\t\t\tparentNode: container,\n\t\t\tchildNodes: [],\n\t\t\t_children: { _mask: root._mask },\n\t\t\tcontains: () => true,\n\t\t\tinsertBefore(child, before) {\n\t\t\t\tthis.childNodes.push(child);\n\t\t\t\t_this._container.insertBefore(child, before);\n\t\t\t},\n\t\t\tremoveChild(child) {\n\t\t\t\tthis.childNodes.splice(this.childNodes.indexOf(child) >>> 1, 1);\n\t\t\t\t_this._container.removeChild(child);\n\t\t\t}\n\t\t};\n\t}\n\n\t// Render our wrapping element into temp.\n\trender(\n\t\tcreateElement(ContextProvider, { context: _this.context }, props._vnode),\n\t\t_this._temp\n\t);\n}\n\n/**\n * Create a `Portal` to continue rendering the vnode tree at a different DOM node\n * @param {import('./internal').VNode} vnode The vnode to render\n * @param {import('./internal').PreactElement} container The DOM node to continue rendering in to.\n */\nexport function createPortal(vnode, container) {\n\tconst el = createElement(Portal, { _vnode: vnode, _container: container });\n\tel.containerInfo = container;\n\treturn el;\n}\n", "import {\n\trender as preactRender,\n\thydrate as preactHydrate,\n\toptions,\n\ttoChildArray,\n\tComponent\n} from 'preact';\nimport {\n\tuseCallback,\n\tuseContext,\n\tuseDebugValue,\n\tuseEffect,\n\tuseId,\n\tuseImperativeHandle,\n\tuseLayoutEffect,\n\tuseMemo,\n\tuseReducer,\n\tuseRef,\n\tuseState\n} from 'preact/hooks';\nimport {\n\tuseDeferredValue,\n\tuseInsertionEffect,\n\tuseSyncExternalStore,\n\tuseTransition\n} from './index';\n\nexport const REACT_ELEMENT_TYPE =\n\t(typeof Symbol != 'undefined' && Symbol.for && Symbol.for('react.element')) ||\n\t0xeac7;\n\nconst CAMEL_PROPS =\n\t/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/;\nconst ON_ANI = /^on(Ani|Tra|Tou|BeforeInp|Compo)/;\nconst CAMEL_REPLACE = /[A-Z0-9]/g;\nconst IS_DOM = typeof document !== 'undefined';\n\n// Input types for which onchange should not be converted to oninput.\n// type=\"file|checkbox|radio\", plus \"range\" in IE11.\n// (IE11 doesn't support Symbol, which we use here to turn `rad` into `ra` which matches \"range\")\nconst onChangeInputType = type =>\n\t(typeof Symbol != 'undefined' && typeof Symbol() == 'symbol'\n\t\t? /fil|che|rad/\n\t\t: /fil|che|ra/\n\t).test(type);\n\n// Some libraries like `react-virtualized` explicitly check for this.\nComponent.prototype.isReactComponent = {};\n\n// `UNSAFE_*` lifecycle hooks\n// Preact only ever invokes the unprefixed methods.\n// Here we provide a base \"fallback\" implementation that calls any defined UNSAFE_ prefixed method.\n// - If a component defines its own `componentDidMount()` (including via defineProperty), use that.\n// - If a component defines `UNSAFE_componentDidMount()`, `componentDidMount` is the alias getter/setter.\n// - If anything assigns to an `UNSAFE_*` property, the assignment is forwarded to the unprefixed property.\n// See https://github.com/preactjs/preact/issues/1941\n[\n\t'componentWillMount',\n\t'componentWillReceiveProps',\n\t'componentWillUpdate'\n].forEach(key => {\n\tObject.defineProperty(Component.prototype, key, {\n\t\tconfigurable: true,\n\t\tget() {\n\t\t\treturn this['UNSAFE_' + key];\n\t\t},\n\t\tset(v) {\n\t\t\tObject.defineProperty(this, key, {\n\t\t\t\tconfigurable: true,\n\t\t\t\twritable: true,\n\t\t\t\tvalue: v\n\t\t\t});\n\t\t}\n\t});\n});\n\n/**\n * Proxy render() since React returns a Component reference.\n * @param {import('./internal').VNode} vnode VNode tree to render\n * @param {import('./internal').PreactElement} parent DOM node to render vnode tree into\n * @param {() => void} [callback] Optional callback that will be called after rendering\n * @returns {import('./internal').Component | null} The root component reference or null\n */\nexport function render(vnode, parent, callback) {\n\t// React destroys any existing DOM nodes, see #1727\n\t// ...but only on the first render, see #1828\n\tif (parent._children == null) {\n\t\tparent.textContent = '';\n\t}\n\n\tpreactRender(vnode, parent);\n\tif (typeof callback == 'function') callback();\n\n\treturn vnode ? vnode._component : null;\n}\n\nexport function hydrate(vnode, parent, callback) {\n\tpreactHydrate(vnode, parent);\n\tif (typeof callback == 'function') callback();\n\n\treturn vnode ? vnode._component : null;\n}\n\nlet oldEventHook = options.event;\noptions.event = e => {\n\tif (oldEventHook) e = oldEventHook(e);\n\n\te.persist = empty;\n\te.isPropagationStopped = isPropagationStopped;\n\te.isDefaultPrevented = isDefaultPrevented;\n\treturn (e.nativeEvent = e);\n};\n\nfunction empty() {}\n\nfunction isPropagationStopped() {\n\treturn this.cancelBubble;\n}\n\nfunction isDefaultPrevented() {\n\treturn this.defaultPrevented;\n}\n\nconst classNameDescriptorNonEnumberable = {\n\tenumerable: false,\n\tconfigurable: true,\n\tget() {\n\t\treturn this.class;\n\t}\n};\n\nfunction handleDomVNode(vnode) {\n\tlet props = vnode.props,\n\t\ttype = vnode.type,\n\t\tnormalizedProps = {};\n\n\tlet isNonDashedType = type.indexOf('-') === -1;\n\tfor (let i in props) {\n\t\tlet value = props[i];\n\n\t\tif (\n\t\t\t(i === 'value' && 'defaultValue' in props && value == null) ||\n\t\t\t// Emulate React's behavior of not rendering the contents of noscript tags on the client.\n\t\t\t(IS_DOM && i === 'children' && type === 'noscript') ||\n\t\t\ti === 'class' ||\n\t\t\ti === 'className'\n\t\t) {\n\t\t\t// Skip applying value if it is null/undefined and we already set\n\t\t\t// a default value\n\t\t\tcontinue;\n\t\t}\n\n\t\tlet lowerCased = i.toLowerCase();\n\t\tif (i === 'defaultValue' && 'value' in props && props.value == null) {\n\t\t\t// `defaultValue` is treated as a fallback `value` when a value prop is present but null/undefined.\n\t\t\t// `defaultValue` for Elements with no value prop is the same as the DOM defaultValue property.\n\t\t\ti = 'value';\n\t\t} else if (i === 'download' && value === true) {\n\t\t\t// Calling `setAttribute` with a truthy value will lead to it being\n\t\t\t// passed as a stringified value, e.g. `download=\"true\"`. React\n\t\t\t// converts it to an empty string instead, otherwise the attribute\n\t\t\t// value will be used as the file name and the file will be called\n\t\t\t// \"true\" upon downloading it.\n\t\t\tvalue = '';\n\t\t} else if (lowerCased === 'translate' && value === 'no') {\n\t\t\tvalue = false;\n\t\t} else if (lowerCased[0] === 'o' && lowerCased[1] === 'n') {\n\t\t\tif (lowerCased === 'ondoubleclick') {\n\t\t\t\ti = 'ondblclick';\n\t\t\t} else if (\n\t\t\t\tlowerCased === 'onchange' &&\n\t\t\t\t(type === 'input' || type === 'textarea') &&\n\t\t\t\t!onChangeInputType(props.type)\n\t\t\t) {\n\t\t\t\tlowerCased = i = 'oninput';\n\t\t\t} else if (lowerCased === 'onfocus') {\n\t\t\t\ti = 'onfocusin';\n\t\t\t} else if (lowerCased === 'onblur') {\n\t\t\t\ti = 'onfocusout';\n\t\t\t} else if (ON_ANI.test(i)) {\n\t\t\t\ti = lowerCased;\n\t\t\t}\n\t\t} else if (isNonDashedType && CAMEL_PROPS.test(i)) {\n\t\t\ti = i.replace(CAMEL_REPLACE, '-$&').toLowerCase();\n\t\t} else if (value === null) {\n\t\t\tvalue = undefined;\n\t\t}\n\n\t\t// Add support for onInput and onChange, see #3561\n\t\t// if we have an oninput prop already change it to oninputCapture\n\t\tif (lowerCased === 'oninput') {\n\t\t\ti = lowerCased;\n\t\t\tif (normalizedProps[i]) {\n\t\t\t\ti = 'oninputCapture';\n\t\t\t}\n\t\t}\n\n\t\tnormalizedProps[i] = value;\n\t}\n\n\t// Add support for array select values: <select multiple value={[]} />\n\tif (\n\t\ttype == 'select' &&\n\t\tnormalizedProps.multiple &&\n\t\tArray.isArray(normalizedProps.value)\n\t) {\n\t\t// forEach() always returns undefined, which we abuse here to unset the value prop.\n\t\tnormalizedProps.value = toChildArray(props.children).forEach(child => {\n\t\t\tchild.props.selected =\n\t\t\t\tnormalizedProps.value.indexOf(child.props.value) != -1;\n\t\t});\n\t}\n\n\t// Adding support for defaultValue in select tag\n\tif (type == 'select' && normalizedProps.defaultValue != null) {\n\t\tnormalizedProps.value = toChildArray(props.children).forEach(child => {\n\t\t\tif (normalizedProps.multiple) {\n\t\t\t\tchild.props.selected =\n\t\t\t\t\tnormalizedProps.defaultValue.indexOf(child.props.value) != -1;\n\t\t\t} else {\n\t\t\t\tchild.props.selected =\n\t\t\t\t\tnormalizedProps.defaultValue == child.props.value;\n\t\t\t}\n\t\t});\n\t}\n\n\tif (props.class && !props.className) {\n\t\tnormalizedProps.class = props.class;\n\t\tObject.defineProperty(\n\t\t\tnormalizedProps,\n\t\t\t'className',\n\t\t\tclassNameDescriptorNonEnumberable\n\t\t);\n\t} else if (props.className && !props.class) {\n\t\tnormalizedProps.class = normalizedProps.className = props.className;\n\t} else if (props.class && props.className) {\n\t\tnormalizedProps.class = normalizedProps.className = props.className;\n\t}\n\n\tvnode.props = normalizedProps;\n}\n\nlet oldVNodeHook = options.vnode;\noptions.vnode = vnode => {\n\t// only normalize props on Element nodes\n\tif (typeof vnode.type === 'string') {\n\t\thandleDomVNode(vnode);\n\t}\n\n\tvnode.$$typeof = REACT_ELEMENT_TYPE;\n\n\tif (oldVNodeHook) oldVNodeHook(vnode);\n};\n\n// Only needed for react-relay\nlet currentComponent;\nconst oldBeforeRender = options._render;\noptions._render = function (vnode) {\n\tif (oldBeforeRender) {\n\t\toldBeforeRender(vnode);\n\t}\n\tcurrentComponent = vnode._component;\n};\n\nconst oldDiffed = options.diffed;\n/** @type {(vnode: import('./internal').VNode) => void} */\noptions.diffed = function (vnode) {\n\tif (oldDiffed) {\n\t\toldDiffed(vnode);\n\t}\n\n\tconst props = vnode.props;\n\tconst dom = vnode._dom;\n\n\tif (\n\t\tdom != null &&\n\t\tvnode.type === 'textarea' &&\n\t\t'value' in props &&\n\t\tprops.value !== dom.value\n\t) {\n\t\tdom.value = props.value == null ? '' : props.value;\n\t}\n\n\tcurrentComponent = null;\n};\n\n// This is a very very private internal function for React it\n// is used to sort-of do runtime dependency injection.\nexport const __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = {\n\tReactCurrentDispatcher: {\n\t\tcurrent: {\n\t\t\treadContext(context) {\n\t\t\t\treturn currentComponent._globalContext[context._id].props.value;\n\t\t\t},\n\t\t\tuseCallback,\n\t\t\tuseContext,\n\t\t\tuseDebugValue,\n\t\t\tuseDeferredValue,\n\t\t\tuseEffect,\n\t\t\tuseId,\n\t\t\tuseImperativeHandle,\n\t\t\tuseInsertionEffect,\n\t\t\tuseLayoutEffect,\n\t\t\tuseMemo,\n\t\t\t// useMutableSource, // experimental-only and replaced by uSES, likely not worth supporting\n\t\t\tuseReducer,\n\t\t\tuseRef,\n\t\t\tuseState,\n\t\t\tuseSyncExternalStore,\n\t\t\tuseTransition\n\t\t}\n\t}\n};\n", "import {\n\tcreateElement,\n\trender as preactRender,\n\tcloneElement as preactCloneElement,\n\tcreateRef,\n\tComponent,\n\tcreateContext,\n\tFragment\n} from 'preact';\nimport {\n\tuseState,\n\tuseId,\n\tuseReducer,\n\tuseEffect,\n\tuseLayoutEffect,\n\tuseRef,\n\tuseImperativeHandle,\n\tuseMemo,\n\tuseCallback,\n\tuseContext,\n\tuseDebugValue\n} from 'preact/hooks';\nimport {\n\tuseInsertionEffect,\n\tstartTransition,\n\tuseDeferredValue,\n\tuseSyncExternalStore,\n\tuseTransition\n} from './hooks';\nimport { PureComponent } from './PureComponent';\nimport { memo } from './memo';\nimport { forwardRef } from './forwardRef';\nimport { Children } from './Children';\nimport { Suspense, lazy } from './suspense';\nimport { SuspenseList } from './suspense-list';\nimport { createPortal } from './portals';\nimport {\n\thydrate,\n\trender,\n\tREACT_ELEMENT_TYPE,\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\n} from './render';\n\nconst version = '18.3.1'; // trick libraries to think we are react\n\n/**\n * Legacy version of createElement.\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component constructor\n */\nfunction createFactory(type) {\n\treturn createElement.bind(null, type);\n}\n\n/**\n * Check if the passed element is a valid (p)react node.\n * @param {*} element The element to check\n * @returns {boolean}\n */\nfunction isValidElement(element) {\n\treturn !!element && element.$$typeof === REACT_ELEMENT_TYPE;\n}\n\n/**\n * Check if the passed element is a Fragment node.\n * @param {*} element The element to check\n * @returns {boolean}\n */\nfunction isFragment(element) {\n\treturn isValidElement(element) && element.type === Fragment;\n}\n\n/**\n * Check if the passed element is a Memo node.\n * @param {*} element The element to check\n * @returns {boolean}\n */\nfunction isMemo(element) {\n\treturn (\n\t\t!!element &&\n\t\t!!element.displayName &&\n\t\t(typeof element.displayName === 'string' ||\n\t\t\telement.displayName instanceof String) &&\n\t\telement.displayName.startsWith('Memo(')\n\t);\n}\n\n/**\n * Wrap `cloneElement` to abort if the passed element is not a valid element and apply\n * all vnode normalizations.\n * @param {import('./internal').VNode} element The vnode to clone\n * @param {object} props Props to add when cloning\n * @param {Array<import('./internal').ComponentChildren>} rest Optional component children\n */\nfunction cloneElement(element) {\n\tif (!isValidElement(element)) return element;\n\treturn preactCloneElement.apply(null, arguments);\n}\n\n/**\n * Remove a component tree from the DOM, including state and event handlers.\n * @param {import('./internal').PreactElement} container\n * @returns {boolean}\n */\nfunction unmountComponentAtNode(container) {\n\tif (container._children) {\n\t\tpreactRender(null, container);\n\t\treturn true;\n\t}\n\treturn false;\n}\n\n/**\n * Get the matching DOM node for a component\n * @param {import('./internal').Component} component\n * @returns {import('./internal').PreactElement | null}\n */\nfunction findDOMNode(component) {\n\treturn (\n\t\t(component &&\n\t\t\t(component.base || (component.nodeType === 1 && component))) ||\n\t\tnull\n\t);\n}\n\n/**\n * Deprecated way to control batched rendering inside the reconciler, but we\n * already schedule in batches inside our rendering code\n * @template Arg\n * @param {(arg: Arg) => void} callback function that triggers the updated\n * @param {Arg} [arg] Optional argument that can be passed to the callback\n */\n// eslint-disable-next-line camelcase\nconst unstable_batchedUpdates = (callback, arg) => callback(arg);\n\n/**\n * In React, `flushSync` flushes the entire tree and forces a rerender. It's\n * implmented here as a no-op.\n * @template Arg\n * @template Result\n * @param {(arg: Arg) => Result} callback function that runs before the flush\n * @param {Arg} [arg] Optional argument that can be passed to the callback\n * @returns\n */\nconst flushSync = (callback, arg) => callback(arg);\n\n/**\n * Strict Mode is not implemented in Preact, so we provide a stand-in for it\n * that just renders its children without imposing any restrictions.\n */\nconst StrictMode = Fragment;\n\n// compat to react-is\nexport const isElement = isValidElement;\n\nexport * from 'preact/hooks';\nexport {\n\tversion,\n\tChildren,\n\trender,\n\thydrate,\n\tunmountComponentAtNode,\n\tcreatePortal,\n\tcreateElement,\n\tcreateContext,\n\tcreateFactory,\n\tcloneElement,\n\tcreateRef,\n\tFragment,\n\tisValidElement,\n\tisFragment,\n\tisMemo,\n\tfindDOMNode,\n\tComponent,\n\tPureComponent,\n\tmemo,\n\tforwardRef,\n\tflushSync,\n\tuseInsertionEffect,\n\tstartTransition,\n\tuseDeferredValue,\n\tuseSyncExternalStore,\n\tuseTransition,\n\t// eslint-disable-next-line camelcase\n\tunstable_batchedUpdates,\n\tStrictMode,\n\tSuspense,\n\tSuspenseList,\n\tlazy,\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\n};\n\n// React copies the named exports to the default one.\nexport default {\n\tuseState,\n\tuseId,\n\tuseReducer,\n\tuseEffect,\n\tuseLayoutEffect,\n\tuseInsertionEffect,\n\tuseTransition,\n\tuseDeferredValue,\n\tuseSyncExternalStore,\n\tstartTransition,\n\tuseRef,\n\tuseImperativeHandle,\n\tuseMemo,\n\tuseCallback,\n\tuseContext,\n\tuseDebugValue,\n\tversion,\n\tChildren,\n\trender,\n\thydrate,\n\tunmountComponentAtNode,\n\tcreatePortal,\n\tcreateElement,\n\tcreateContext,\n\tcreateFactory,\n\tcloneElement,\n\tcreateRef,\n\tFragment,\n\tisValidElement,\n\tisElement,\n\tisFragment,\n\tisMemo,\n\tfindDOMNode,\n\tComponent,\n\tPureComponent,\n\tmemo,\n\tforwardRef,\n\tflushSync,\n\tunstable_batchedUpdates,\n\tStrictMode,\n\tSuspense,\n\tSuspenseList,\n\tlazy,\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\n};\n"], "names": ["assign", "obj", "props", "i", "shallow<PERSON>iffers", "a", "b", "useSyncExternalStore", "subscribe", "getSnapshot", "value", "_useState", "useState", "_instance", "__", "_getSnapshot", "forceUpdate", "useLayoutEffect", "didSnapshotChange", "useEffect", "inst", "x", "y", "latestGetSnapshot", "prevValue", "nextValue", "error", "startTransition", "cb", "useDeferredValue", "val", "useTransition", "useInsertionEffect", "PureComponent", "p", "c", "this", "context", "memo", "comparer", "shouldUpdate", "nextProps", "ref", "updateRef", "call", "current", "Memoed", "shouldComponentUpdate", "createElement", "displayName", "name", "prototype", "isReactComponent", "__f", "Component", "isPureReactComponent", "state", "oldDiffHook", "options", "__b", "vnode", "type", "REACT_FORWARD_SYMBOL", "Symbol", "for", "forwardRef", "fn", "Forwarded", "clone", "$$typeof", "render", "mapFn", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "Children", "for<PERSON>ach", "count", "length", "only", "normalized", "toArray", "oldCatchError", "__e", "newVNode", "oldVNode", "errorInfo", "then", "component", "__c", "__k", "oldUnmount", "unmount", "detachedClone", "detachedParent", "parentDom", "__H", "effect", "__P", "child", "removeOriginal", "originalParent", "__v", "append<PERSON><PERSON><PERSON>", "Suspense", "__u", "_suspenders", "suspended", "__a", "lazy", "loader", "prom", "Lazy", "exports", "default", "e", "SuspenseList", "_next", "_map", "__R", "promise", "suspendingVNode", "suspendingComponent", "push", "resolve", "resolved", "onResolved", "onSuspensionComplete", "suspendedVNode", "__O", "setState", "pop", "componentWillUnmount", "document", "detachedComponent", "fallback", "Fragment", "list", "node", "delete", "revealOrder", "size", "ContextProvider", "getChildContext", "Portal", "_this", "container", "_container", "_temp", "root", "__m", "nodeType", "parentNode", "childNodes", "contains", "insertBefore", "before", "<PERSON><PERSON><PERSON><PERSON>", "splice", "indexOf", "createPortal", "el", "containerInfo", "delegated", "get", "unsuspend", "wrappedUnsuspend", "Map", "reverse", "set", "componentDidUpdate", "componentDidMount", "REACT_ELEMENT_TYPE", "CAMEL_PROPS", "ON_ANI", "CAMEL_REPLACE", "IS_DOM", "onChangeInputType", "test", "parent", "callback", "textContent", "preactRender", "hydrate", "preactHydrate", "key", "Object", "defineProperty", "configurable", "v", "writable", "oldEventHook", "event", "empty", "isPropagationStopped", "cancelBubble", "isDefaultPrevented", "defaultPrevented", "persist", "nativeEvent", "currentComponent", "classNameDescriptorNonEnumberable", "enumerable", "class", "oldVNodeHook", "normalizedProps", "isNonDashedType", "lowerCased", "toLowerCase", "replace", "undefined", "multiple", "Array", "isArray", "selected", "defaultValue", "className", "handleDomVNode", "oldBeforeRender", "__r", "oldDiffed", "diffed", "dom", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "ReactCur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "readContext", "__n", "useCallback", "useContext", "useDebugValue", "useId", "useImperativeHandle", "useMemo", "useReducer", "useRef", "version", "createFactory", "bind", "isValidElement", "element", "isFragment", "isMemo", "String", "startsWith", "cloneElement", "preactCloneElement", "apply", "arguments", "unmountComponentAtNode", "findDOMNode", "base", "unstable_batchedUpdates", "arg", "flushSync", "StrictMode", "isElement", "index", "createContext", "createRef"], "mappings": "mUAOgB,SAAAA,EAAOC,EAAKC,GAC3B,IAAK,IAAIC,KAAKD,EAAOD,EAAIE,GAAKD,EAAMC,GACpC,OAA6BF,CAC9B,CAQO,SAASG,EAAeC,EAAGC,GACjC,IAAK,IAAIH,KAAKE,EAAG,GAAU,aAANF,KAAsBA,KAAKG,GAAI,OAAW,EAC/D,IAAK,IAAIH,KAAKG,EAAG,GAAU,aAANH,GAAoBE,EAAEF,KAAOG,EAAEH,GAAI,OAAW,EACnE,OAAO,CACR,CCdgB,SAAAI,EAAqBC,EAAWC,GAC/C,IAAMC,EAAQD,IAMdE,EAAqCC,EAAAA,SAAS,CAC7CC,EAAW,CAAEC,GAAQJ,EAAOK,EAAcN,KADlCI,EAASF,EAATE,GAAAA,EAAaG,EAAWL,EAIjCM,GAqBA,OArBAA,EAAAA,gBAAgB,WACfJ,EAASC,GAAUJ,EACnBG,EAAUE,EAAeN,EAErBS,EAAkBL,IACrBG,EAAY,CAAEH,EAAAA,GAEhB,EAAG,CAACL,EAAWE,EAAOD,IAEtBU,EAASA,UAAC,WAKT,OAJID,EAAkBL,IACrBG,EAAY,CAAEH,EAAAA,IAGRL,EAAU,WACZU,EAAkBL,IACrBG,EAAY,CAAEH,EAAAA,GAEhB,EACD,EAAG,CAACL,IAEGE,CACR,CAGA,SAASQ,EAAkBE,GAC1B,IDfkBC,EAAGC,ECefC,EAAoBH,EAAKL,EACzBS,EAAYJ,EAAIN,GACtB,IACC,IAAMW,EAAYF,IAClB,SDnBiBF,ECmBNG,MDnBSF,ECmBEG,KDlBG,IAANJ,GAAW,EAAIA,GAAM,EAAIC,IAAQD,GAAMA,GAAKC,GAAMA,ECqBtE,CAFE,MAAOI,GACR,OACD,CAAA,CACD,CAEgB,SAAAC,EAAgBC,GAC/BA,GACD,CAEgB,SAAAC,EAAiBC,GAChC,OAAOA,CACR,CAEgB,SAAAC,IACf,MAAO,EAAC,EAAOJ,EAChB,CAIa,IAAAK,EAAqBf,EAAAA,yBC/DlBgB,EAAcC,EAAGC,GAChCC,KAAKlC,MAAQgC,EACbE,KAAKC,QAAUF,CAChB,CCCgB,SAAAG,EAAKH,EAAGI,GACvB,SAASC,EAAaC,GACrB,IAAIC,EAAMN,KAAKlC,MAAMwC,IACjBC,EAAYD,GAAOD,EAAUC,IAKjC,OAJKC,GAAaD,IACjBA,EAAIE,KAAOF,EAAI,MAASA,EAAIG,QAAU,MAGlCN,GAIGA,EAASH,KAAKlC,MAAOuC,KAAeE,EAHpCvC,EAAegC,KAAKlC,MAAOuC,EAIpC,CAEA,SAASK,EAAO5C,GAEf,OADAkC,KAAKW,sBAAwBP,EACtBQ,EAAAA,cAAcb,EAAGjC,EACzB,CAIA,OAHA4C,EAAOG,YAAc,SAAWd,EAAEc,aAAed,EAAEe,MAAQ,IAC3DJ,EAAOK,UAAUC,kBAAmB,EACpCN,EAAMO,KAAc,EACbP,CACR,EDvBAb,EAAckB,UAAY,IAAIG,EAAAA,WAENC,sBAAuB,EAC/CtB,EAAckB,UAAUJ,sBAAwB,SAAU7C,EAAOsD,GAChE,OAAOpD,EAAegC,KAAKlC,MAAOA,IAAUE,EAAegC,KAAKoB,MAAOA,EACxE,EEZA,IAAIC,EAAcC,EAAAA,QAAOC,IACzBD,EAAOA,QAAAC,IAAS,SAAAC,GACXA,EAAMC,MAAQD,EAAMC,KAAIR,KAAeO,EAAMlB,MAChDkB,EAAM1D,MAAMwC,IAAMkB,EAAMlB,IACxBkB,EAAMlB,IAAM,MAETe,GAAaA,EAAYG,EAC9B,EAEO,IAAME,EACM,oBAAVC,QACPA,OAAOC,KACPD,OAAOC,IAAI,sBACZ,cASeC,EAAWC,GAC1B,SAASC,EAAUjE,GAClB,IAAIkE,EAAQpE,EAAO,CAAE,EAAEE,GAEvB,cADOkE,EAAM1B,IACNwB,EAAGE,EAAOlE,EAAMwC,KAAO,KAC/B,CAYA,OATAyB,EAAUE,SAAWP,EAKrBK,EAAUG,OAASH,EAEnBA,EAAUhB,UAAUC,iBAAmBe,EAASd,KAAc,EAC9Dc,EAAUlB,YAAc,eAAiBiB,EAAGjB,aAAeiB,EAAGhB,MAAQ,IAC/DiB,CACR,CCzCA,IAAMI,EAAQ,SAACC,EAAUN,GACxB,OAAgB,MAAZM,EAA6B,KAC1BC,EAAAA,aAAaA,EAAAA,aAAaD,GAAUE,IAAIR,GAChD,EAGaS,EAAW,CACvBD,IAAKH,EACLK,QAASL,EACTM,MAAK,SAACL,GACL,OAAOA,EAAWC,eAAaD,GAAUM,OAAS,CACnD,EACAC,KAAI,SAACP,GACJ,IAAMQ,EAAaP,EAAYA,aAACD,GAChC,GAA0B,IAAtBQ,EAAWF,OAAc,KAAM,gBACnC,OAAOE,EAAW,EACnB,EACAC,QAASR,EACVA,cChBMS,EAAgBxB,EAAAA,QAAOyB,IAC7BzB,EAAAA,QAAOyB,IAAe,SAAUzD,EAAO0D,EAAUC,EAAUC,GAC1D,GAAI5D,EAAM6D,KAKT,IAHA,IAAIC,EACA5B,EAAQwB,EAEJxB,EAAQA,EAAK9C,IACpB,IAAK0E,EAAY5B,EAAK6B,MAAgBD,EAASC,IAM9C,OALqB,MAAjBL,EAAQD,MACXC,EAAQD,IAAQE,EAAQF,IACxBC,EAAQM,IAAaL,EAAQK,KAGvBF,EAASC,IAAkB/D,EAAO0D,GAI5CF,EAAcxD,EAAO0D,EAAUC,EAAUC,EAC1C,EAEA,IAAMK,EAAajC,EAAOA,QAACkC,QAmB3B,SAASC,EAAcjC,EAAOkC,EAAgBC,GA4B7C,OA3BInC,IACCA,EAAK6B,KAAe7B,EAAK6B,IAAAO,MAC5BpC,EAAK6B,IAAAO,IAAAlF,GAA0B8D,QAAQ,SAAAqB,GACR,mBAAnBA,EAAMR,KAAyBQ,EAAMR,KACjD,GAEA7B,EAAK6B,IAAAO,IAAsB,MAIJ,OADxBpC,EAAQ5D,EAAO,CAAA,EAAI4D,IACV6B,MACJ7B,EAAK6B,IAAAS,MAA2BH,IACnCnC,EAAK6B,IAAAS,IAAyBJ,GAG/BlC,EAAK6B,IAAAN,KAAqB,EAE1BvB,EAAK6B,IAAc,MAGpB7B,EAAK8B,IACJ9B,EAAK8B,KACL9B,EAAK8B,IAAWhB,IAAI,SAAAyB,GAAK,OACxBN,EAAcM,EAAOL,EAAgBC,EAAU,IAI3CnC,CACR,CAEA,SAASwC,EAAexC,EAAOkC,EAAgBO,GAoB9C,OAnBIzC,GAASyC,IACZzC,EAAK0C,IAAa,KAClB1C,EAAK8B,IACJ9B,EAAK8B,KACL9B,EAAK8B,IAAWhB,IAAI,SAAAyB,GAAK,OACxBC,EAAeD,EAAOL,EAAgBO,EAAe,GAGnDzC,EAAK6B,KACJ7B,EAAK6B,IAAAS,MAA2BJ,IAC/BlC,EAAKuB,KACRkB,EAAeE,YAAY3C,EAAKuB,KAEjCvB,EAAK6B,IAAAN,KAAqB,EAC1BvB,EAAK6B,IAAAS,IAAyBG,IAK1BzC,CACR,CAGO,SAAS4C,IAEfpE,KAAIqE,IAA2B,EAC/BrE,KAAKsE,EAAc,KACnBtE,KAAIuB,IAAuB,IAC5B,CAqIgB,SAAAgD,EAAU/C,GAEzB,IAAI4B,EAAY5B,EAAK9C,GAAA2E,IACrB,OAAOD,GAAaA,EAASoB,KAAepB,EAASoB,IAAYhD,EAClE,CAEgB,SAAAiD,EAAKC,GACpB,IAAIC,EACAvB,EACA9D,EAEJ,SAASsF,EAAK9G,GAab,GAZK6G,IACJA,EAAOD,KACFvB,KACJ,SAAA0B,GACCzB,EAAYyB,EAAQC,SAAWD,CAChC,EACA,SAAAE,GACCzF,EAAQyF,CACT,GAIEzF,EACH,MAAMA,EAGP,IAAK8D,EACJ,MAAMuB,EAGP,OAAO/D,EAAaA,cAACwC,EAAWtF,EACjC,CAIA,OAFA8G,EAAK/D,YAAc,OACnB+D,EAAI3D,KAAc,EACX2D,CACR,UC1QgBI,IACfhF,KAAKiF,EAAQ,KACbjF,KAAKkF,EAAO,IACb,CDcA5D,EAAOA,QAACkC,QAAU,SAAUhC,GAE3B,IAAM4B,EAAY5B,EAAK6B,IACnBD,GAAaA,EAAS+B,KACzB/B,EAAS+B,MAON/B,GEpCuB,GFoCV5B,EAAK6C,MACrB7C,EAAMC,KAAO,MAGV8B,GAAYA,EAAW/B,EAC5B,GAmEA4C,EAASrD,UAAY,IAAIG,EAAWA,WAOlBmC,IAAoB,SAAU+B,EAASC,GACxD,IAAMC,EAAsBD,EAAehC,IAGrCtD,EAAIC,KAEW,MAAjBD,EAAEuE,IACLvE,EAAEuE,EAAc,IAEjBvE,EAAEuE,EAAYiB,KAAKD,GAEnB,IAAME,EAAUjB,EAAUxE,EAACmE,KAEvBuB,GAAW,EACTC,EAAa,WACdD,IAEJA,GAAW,EACXH,EAAmBH,IAAc,KAE7BK,EACHA,EAAQG,GAERA,IAEF,EAEAL,EAAmBH,IAAcO,EAEjC,IAAMC,EAAuB,WAC5B,MAAO5F,EAACsE,IAA0B,CAGjC,GAAItE,EAAEqB,MAAKoD,IAAa,CACvB,IAAMoB,EAAiB7F,EAAEqB,MAAKoD,IAC9BzE,EAACmE,IAAAZ,IAAkB,GAAKU,EACvB4B,EACAA,EAAcvC,IAAAS,IACd8B,EAAcvC,IAAAwC,IAEhB,CAIA,IAAItB,EACJ,IAHAxE,EAAE+F,SAAS,CAAEtB,IAAazE,EAACwB,IAAuB,OAG1CgD,EAAYxE,EAAEuE,EAAYyB,OACjCxB,EAAU3F,aAEZ,CACD,EAQEmB,EAACsE,OE5KwB,GF6KxBgB,EAAehB,KAEjBtE,EAAE+F,SAAS,CAAEtB,IAAazE,EAACwB,IAAuBxB,EAACmE,IAAAZ,IAAkB,KAEtE8B,EAAQjC,KAAKuC,EAAYA,EAC1B,EAEAtB,EAASrD,UAAUiF,qBAAuB,WACzChG,KAAKsE,EAAc,EACpB,EAOAF,EAASrD,UAAUmB,OAAS,SAAUpE,EAAOsD,GAC5C,GAAIpB,KAAIuB,IAAsB,CAI7B,GAAIvB,KAAIkE,IAAAZ,IAAmB,CAC1B,IAAMI,EAAiBuC,SAASrF,cAAc,OACxCsF,EAAoBlG,KAAIkE,IAAAZ,IAAkB,GAAED,IAClDrD,KAAIkE,IAAAZ,IAAkB,GAAKG,EAC1BzD,KAAIuB,IACJmC,EACCwC,EAAiBL,IAAsBK,EAAiBpC,IAE3D,CAEA9D,KAAIuB,IAAuB,IAC5B,CAIA,IAAM4E,EACL/E,EAAKoD,KAAe5D,EAAAA,cAAcwF,EAAAA,SAAU,KAAMtI,EAAMqI,UAGzD,OAFIA,IAAUA,EAAQ9B,MAAW,IAE1B,CACNzD,EAAaA,cAACwF,EAAQA,SAAE,KAAMhF,EAAKoD,IAAc,KAAO1G,EAAMsE,UAC9D+D,EAEF,ECxMA,IAAMX,EAAU,SAACa,EAAMtC,EAAOuC,GAc7B,KAbMA,EAdgB,KAcSA,EAfR,IAqBtBD,EAAKnB,EAAKqB,OAAOxC,GAQhBsC,EAAKvI,MAAM0I,cACmB,MAA9BH,EAAKvI,MAAM0I,YAAY,KAAcH,EAAKnB,EAAKuB,MASjD,IADAH,EAAOD,EAAKpB,EACLqB,GAAM,CACZ,KAAOA,EAAK5D,OAAS,GACpB4D,EAAKP,KAALO,GAED,GAAIA,EA1CiB,GA0CMA,EA3CL,GA4CrB,MAEDD,EAAKpB,EAAQqB,EAAOA,EA5CJ,EA6CjB,CACD,EE/CA,SAASI,EAAgB5I,GAExB,OADAkC,KAAK2G,gBAAkB,WAAM,OAAA7I,EAAMmC,OAAO,EACnCnC,EAAMsE,QACd,CASA,SAASwE,EAAO9I,GACf,IAAM+I,EAAQ7G,KACV8G,EAAYhJ,EAAMiJ,EActB,GAZAF,EAAMb,qBAAuB,WAC5B9D,EAAMA,OAAC,KAAM2E,EAAMG,GACnBH,EAAMG,EAAQ,KACdH,EAAME,EAAa,IACpB,EAIIF,EAAME,GAAcF,EAAME,IAAeD,GAC5CD,EAAMb,wBAGFa,EAAMG,EAAO,CAGjB,IADA,IAAIC,EAAOJ,EAAK3C,IACA,OAAT+C,IAAkBA,EAAIC,KAA2B,OAAjBD,EAAIvI,IAC1CuI,EAAOA,EAAIvI,GAGZmI,EAAME,EAAaD,EAGnBD,EAAMG,EAAQ,CACbG,SAAU,EACVC,WAAYN,EACZO,WAAY,GACZ/D,IAAW,CAAE4D,IAAOD,EAAIC,KACxBI,SAAU,WAAF,QAAY,EACpBC,aAAA,SAAaxD,EAAOyD,GACnBxH,KAAKqH,WAAW9B,KAAKxB,GACrB8C,EAAME,EAAWQ,aAAaxD,EAAOyD,EACtC,EACAC,qBAAY1D,GACX/D,KAAKqH,WAAWK,OAAO1H,KAAKqH,WAAWM,QAAQ5D,KAAW,EAAG,GAC7D8C,EAAME,EAAWU,YAAY1D,EAC9B,EAEF,CAGA7B,SACCtB,EAAAA,cAAc8F,EAAiB,CAAEzG,QAAS4G,EAAM5G,SAAWnC,EAAKoG,KAChE2C,EAAMG,EAER,CAOO,SAASY,EAAapG,EAAOsF,GACnC,IAAMe,EAAKjH,EAAaA,cAACgG,EAAQ,CAAE1C,IAAQ1C,EAAOuF,EAAYD,IAE9D,OADAe,EAAGC,cAAgBhB,EACZe,CACR,EFnBA7C,EAAajE,UAAY,IAAIG,aAEPsD,IAAc,SAAUT,GAC7C,IAAMsC,EAAOrG,KACP+H,EAAYxD,EAAU8B,EAAInC,KAE5BoC,EAAOD,EAAKnB,EAAK8C,IAAIjE,GAGzB,OAFAuC,EA5DuB,KA8DhB,SAAA2B,GACN,IAAMC,EAAmB,WACnB7B,EAAKvI,MAAM0I,aAKfF,EAAKf,KAAK0C,GACVzC,EAAQa,EAAMtC,EAAOuC,IAHrB2B,GAKF,EACIF,EACHA,EAAUG,GAEVA,GAEF,CACD,EAEAlD,EAAajE,UAAUmB,OAAS,SAAUpE,GACzCkC,KAAKiF,EAAQ,KACbjF,KAAKkF,EAAO,IAAIiD,IAEhB,IAAM/F,EAAWC,EAAAA,aAAavE,EAAMsE,UAChCtE,EAAM0I,aAAwC,MAAzB1I,EAAM0I,YAAY,IAI1CpE,EAASgG,UAIV,IAAK,IAAIrK,EAAIqE,EAASM,OAAQ3E,KAY7BiC,KAAKkF,EAAKmD,IAAIjG,EAASrE,GAAKiC,KAAKiF,EAAQ,CAAC,EAAG,EAAGjF,KAAKiF,IAEtD,OAAOnH,EAAMsE,QACd,EAEA4C,EAAajE,UAAUuH,mBACtBtD,EAAajE,UAAUwH,kBAAoB,eAAY1B,EAAA7G,KAOtDA,KAAKkF,EAAK1C,QAAQ,SAAC8D,EAAMvC,GACxByB,EAAQqB,EAAM9C,EAAOuC,EACtB,EACD,EGnGY,IAAAkC,EACM,oBAAV7G,QAAyBA,OAAOC,KAAOD,OAAOC,IAAI,kBAC1D,MAEK6G,EACL,8RACKC,EAAS,mCACTC,EAAgB,YAChBC,EAA6B,oBAAb3C,SAKhB4C,EAAoB,SAAApH,GACzB,OAAkB,oBAAVE,QAA4C,iBAAZA,SACrC,cACA,cACDmH,KAAKrH,EAAK,EAuCG,SAAAS,EAAOV,EAAOuH,EAAQC,GAUrC,OAPwB,MAApBD,EAAMzF,MACTyF,EAAOE,YAAc,IAGtBC,SAAa1H,EAAOuH,GACG,mBAAZC,GAAwBA,IAE5BxH,EAAQA,EAAK6B,IAAc,IACnC,CAEgB,SAAA8F,EAAQ3H,EAAOuH,EAAQC,GAItC,OAHAI,UAAc5H,EAAOuH,GACE,mBAAZC,GAAwBA,IAE5BxH,EAAQA,EAAK6B,IAAc,IACnC,CAtDAnC,EAAAA,UAAUH,UAAUC,iBAAmB,CAAA,EASvC,CACC,qBACA,4BACA,uBACCwB,QAAQ,SAAA6G,GACTC,OAAOC,eAAerI,EAASA,UAACH,UAAWsI,EAAK,CAC/CG,cAAc,EACdxB,IAAG,WACF,OAAOhI,KAAK,UAAYqJ,EACzB,EACAhB,IAAG,SAACoB,GACHH,OAAOC,eAAevJ,KAAMqJ,EAAK,CAChCG,cAAc,EACdE,UAAU,EACVpL,MAAOmL,GAET,GAEF,GA6BA,IAAIE,EAAerI,EAAOA,QAACsI,MAU3B,SAASC,IAAQ,CAEjB,SAASC,IACR,OAAW9J,KAAC+J,YACb,CAEA,SAASC,IACR,OAAOhK,KAAKiK,gBACb,CAjBA3I,EAAOA,QAACsI,MAAQ,SAAA7E,GAMf,OALI4E,IAAc5E,EAAI4E,EAAa5E,IAEnCA,EAAEmF,QAAUL,EACZ9E,EAAE+E,qBAAuBA,EACzB/E,EAAEiF,mBAAqBA,EACfjF,EAAEoF,YAAcpF,CACzB,EAYA,IAoIIqF,EApIEC,EAAoC,CACzCC,YAAY,EACZd,cAAc,EACdxB,eACC,OAAWhI,KAACuK,KACb,GAkHGC,EAAelJ,EAAAA,QAAQE,MAC3BF,EAAOA,QAACE,MAAQ,SAAAA,GAEW,iBAAfA,EAAMC,MAlHlB,SAAwBD,GACvB,IAAI1D,EAAQ0D,EAAM1D,MACjB2D,EAAOD,EAAMC,KACbgJ,EAAkB,CAAE,EAEjBC,GAAyC,IAAvBjJ,EAAKkG,QAAQ,KACnC,IAAK,IAAI5J,KAAKD,EAAO,CACpB,IAAIQ,EAAQR,EAAMC,GAElB,KACQ,UAANA,GAAiB,iBAAkBD,GAAkB,MAATQ,GAE5CsK,GAAgB,aAAN7K,GAA6B,aAAT0D,GACzB,UAAN1D,GACM,cAANA,GALD,CAYA,IAAI4M,EAAa5M,EAAE6M,cACT,iBAAN7M,GAAwB,UAAWD,GAAwB,MAAfA,EAAMQ,MAGrDP,EAAI,QACY,aAANA,IAA8B,IAAVO,EAM9BA,EAAQ,GACiB,cAAfqM,GAAwC,OAAVrM,EACxCA,GAAQ,EACoB,MAAlBqM,EAAW,IAAgC,MAAlBA,EAAW,GAC3B,kBAAfA,EACH5M,EAAI,aAEW,aAAf4M,GACU,UAATlJ,GAA6B,aAATA,GACpBoH,EAAkB/K,EAAM2D,MAGA,YAAfkJ,EACV5M,EAAI,YACqB,WAAf4M,EACV5M,EAAI,aACM2K,EAAOI,KAAK/K,KACtBA,EAAI4M,GANJA,EAAa5M,EAAI,UAQR2M,GAAmBjC,EAAYK,KAAK/K,GAC9CA,EAAIA,EAAE8M,QAAQlC,EAAe,OAAOiC,cAChB,OAAVtM,IACVA,OAAQwM,GAKU,YAAfH,GAECF,EADJ1M,EAAI4M,KAEH5M,EAAI,kBAIN0M,EAAgB1M,GAAKO,CA/CrB,CAgDD,CAIS,UAARmD,GACAgJ,EAAgBM,UAChBC,MAAMC,QAAQR,EAAgBnM,SAG9BmM,EAAgBnM,MAAQ+D,EAAAA,aAAavE,EAAMsE,UAAUI,QAAQ,SAAAuB,GAC5DA,EAAMjG,MAAMoN,UAC0C,GAArDT,EAAgBnM,MAAMqJ,QAAQ5D,EAAMjG,MAAMQ,MAC5C,IAIW,UAARmD,GAAoD,MAAhCgJ,EAAgBU,eACvCV,EAAgBnM,MAAQ+D,EAAAA,aAAavE,EAAMsE,UAAUI,QAAQ,SAAAuB,GAE3DA,EAAMjG,MAAMoN,SADTT,EAAgBM,UAE0C,GAA5DN,EAAgBU,aAAaxD,QAAQ5D,EAAMjG,MAAMQ,OAGjDmM,EAAgBU,cAAgBpH,EAAMjG,MAAMQ,KAE/C,IAGGR,EAAMyM,QAAUzM,EAAMsN,WACzBX,EAAgBF,MAAQzM,EAAMyM,MAC9BjB,OAAOC,eACNkB,EACA,YACAJ,KAESvM,EAAMsN,YAActN,EAAMyM,OAE1BzM,EAAMyM,OAASzM,EAAMsN,aAD/BX,EAAgBF,MAAQE,EAAgBW,UAAYtN,EAAMsN,WAK3D5J,EAAM1D,MAAQ2M,CACf,CAMEY,CAAe7J,GAGhBA,EAAMS,SAAWuG,EAEbgC,GAAcA,EAAahJ,EAChC,EAIA,IAAM8J,EAAkBhK,EAAOA,QAAAiK,IAC/BjK,EAAOA,QAAAiK,IAAW,SAAU/J,GACvB8J,GACHA,EAAgB9J,GAEjB4I,EAAmB5I,EAAK6B,GACzB,EAEA,IAAMmI,EAAYlK,EAAOA,QAACmK,OAE1BnK,UAAQmK,OAAS,SAAUjK,GACtBgK,GACHA,EAAUhK,GAGX,IAAM1D,EAAQ0D,EAAM1D,MACd4N,EAAMlK,EAAKuB,IAGT,MAAP2I,GACe,aAAflK,EAAMC,MACN,UAAW3D,GACXA,EAAMQ,QAAUoN,EAAIpN,QAEpBoN,EAAIpN,MAAuB,MAAfR,EAAMQ,MAAgB,GAAKR,EAAMQ,OAG9C8L,EAAmB,IACpB,EAIa,IAAAuB,EAAqD,CACjEC,uBAAwB,CACvBnL,QAAS,CACRoL,qBAAY5L,GACX,OAAOmK,EAAgB0B,IAAgB7L,EAAOoD,KAAMvF,MAAMQ,KAC3D,EACAyN,YAAAA,EAAAA,YACAC,WAAAA,EAAUA,WACVC,cAAAA,EAAaA,cACbxM,iBAAAA,EACAV,UAAAA,EAAAA,UACAmN,MAAAA,EAAAA,MACAC,oBAAAA,EAAmBA,oBACnBvM,mBAAAA,EACAf,gBAAAA,EAAAA,gBACAuN,QAAAA,EAAAA,QAEAC,WAAAA,EAAUA,WACVC,OAAAA,EAAMA,OACN9N,SAAAA,WACAL,qBAAAA,EACAwB,cAAAA,KC1QG4M,EAAU,SAMhB,SAASC,EAAc/K,GACtB,OAAOb,gBAAc6L,KAAK,KAAMhL,EACjC,CAOA,SAASiL,EAAeC,GACvB,QAASA,GAAWA,EAAQ1K,WAAauG,CAC1C,CAOA,SAASoE,EAAWD,GACnB,OAAOD,EAAeC,IAAYA,EAAQlL,OAAS2E,EAAAA,QACpD,CAOA,SAASyG,EAAOF,GACf,QACGA,KACAA,EAAQ9L,cACsB,iBAAxB8L,EAAQ9L,aACf8L,EAAQ9L,uBAAuBiM,SAChCH,EAAQ9L,YAAYkM,WAAW,QAEjC,CASA,SAASC,EAAaL,GACrB,OAAKD,EAAeC,GACbM,eAAmBC,MAAM,KAAMC,WADDR,CAEtC,CAOA,SAASS,GAAuBtG,GAC/B,QAAIA,EAASxD,MACZ4F,SAAa,KAAMpC,MAIrB,CAOA,SAASuG,GAAYjK,GACpB,OACEA,IACCA,EAAUkK,MAAgC,IAAvBlK,EAAU+D,UAAkB/D,IACjD,IAEF,CAUM,IAAAmK,GAA0B,SAACvE,EAAUwE,GAAQ,OAAAxE,EAASwE,EAAI,EAW1DC,GAAY,SAACzE,EAAUwE,UAAQxE,EAASwE,EAAI,EAM5CE,GAAatH,WAGNuH,GAAYjB,EAwCzBkB,GAAe,CACdpP,SAAAA,WACA0N,MAAAA,EAAKA,MACLG,WAAAA,EAAAA,WACAtN,UAAAA,YACAF,gBAAAA,EAAeA,gBACfe,mBAAAA,EACAD,cAAAA,EACAF,iBAAAA,EACAtB,qBAAAA,EACAoB,gBAAAA,EACA+M,OAAAA,SACAH,oBAAAA,EAAmBA,oBACnBC,QAAAA,EAAAA,QACAL,YAAAA,cACAC,WAAAA,EAAUA,WACVC,cAAAA,EAAAA,cACAM,QAAAA,EACAhK,SAAAA,EACAL,OAAAA,EACAiH,QAAAA,EACAiE,uBAAAA,GACAxF,aAAAA,EACAhH,cAAAA,EAAAA,cACAiN,cAAAA,gBACArB,cAAAA,EACAQ,aAAAA,EACAc,UAAAA,YACA1H,SAAAA,EAAQA,SACRsG,eAAAA,EACAiB,UAAAA,GACAf,WAAAA,EACAC,OAAAA,EACAQ,YAAAA,GACAnM,UAAAA,YACArB,cAAAA,EACAK,KAAAA,EACA2B,WAAAA,EACA4L,UAAAA,GACAF,wBAAAA,GACAG,WAAAA,GACAtJ,SAAAA,EACAY,aAAAA,EACAP,KAAAA,EACAkH,mDAAAA"}