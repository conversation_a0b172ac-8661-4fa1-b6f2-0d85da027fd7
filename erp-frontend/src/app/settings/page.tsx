"use client"

import { MainLayout } from "@/components/layout/main-layout"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Settings,
  User,
  Building2,
  Bell,
  Shield,
  Palette,
  Database,
  Mail,
  Globe,
  Save
} from "lucide-react"

export default function SettingsPage() {
  return (
    <ProtectedRoute module="settings" action="read">
      <MainLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-3xl font-bold">الإعدادات</h1>
          <p className="text-muted-foreground">
            إدارة إعدادات النظام والحساب الشخصي
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Company Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building2 className="h-5 w-5 ml-2" />
                إعدادات الشركة
              </CardTitle>
              <CardDescription>
                معلومات الشركة الأساسية
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">اسم الشركة</label>
                <Input defaultValue="شركة التقنية المتقدمة" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">العنوان</label>
                <Input defaultValue="القاهرة، مصر الجديدة، شارع الثورة 123" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">الهاتف</label>
                <Input defaultValue="+20 2 1234 5678" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">البريد الإلكتروني</label>
                <Input defaultValue="<EMAIL>" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">الرقم الضريبي</label>
                <Input defaultValue="123-456-789" />
              </div>
              <Button>
                <Save className="ml-2 h-4 w-4" />
                حفظ التغييرات
              </Button>
            </CardContent>
          </Card>

          {/* User Profile */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 ml-2" />
                الملف الشخصي
              </CardTitle>
              <CardDescription>
                معلومات حسابك الشخصي
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">الاسم الكامل</label>
                <Input defaultValue="أحمد محمد علي" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">البريد الإلكتروني</label>
                <Input defaultValue="<EMAIL>" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">المنصب</label>
                <Input defaultValue="مدير النظام" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">الهاتف</label>
                <Input defaultValue="+20 100 123 4567" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">كلمة المرور الجديدة</label>
                <Input type="password" placeholder="اتركها فارغة للاحتفاظ بالحالية" />
              </div>
              <Button>
                <Save className="ml-2 h-4 w-4" />
                تحديث الملف الشخصي
              </Button>
            </CardContent>
          </Card>

          {/* Notifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Bell className="h-5 w-5 ml-2" />
                إعدادات الإشعارات
              </CardTitle>
              <CardDescription>
                تخصيص الإشعارات والتنبيهات
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">إشعارات البريد الإلكتروني</div>
                  <div className="text-sm text-muted-foreground">
                    تلقي إشعارات عبر البريد الإلكتروني
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  مفعل
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">تنبيهات المخزون</div>
                  <div className="text-sm text-muted-foreground">
                    تنبيهات عند انخفاض المخزون
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  مفعل
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">إشعارات الطلبات الجديدة</div>
                  <div className="text-sm text-muted-foreground">
                    تنبيه عند وصول طلبات جديدة
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  مفعل
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">تقارير دورية</div>
                  <div className="text-sm text-muted-foreground">
                    تقارير أسبوعية وشهرية
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  معطل
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Security */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 ml-2" />
                الأمان والخصوصية
              </CardTitle>
              <CardDescription>
                إعدادات الأمان وحماية البيانات
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">المصادقة الثنائية</div>
                  <div className="text-sm text-muted-foreground">
                    حماية إضافية لحسابك
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  تفعيل
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">تسجيل العمليات</div>
                  <div className="text-sm text-muted-foreground">
                    تسجيل جميع العمليات في النظام
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  مفعل
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">انتهاء الجلسة التلقائي</div>
                  <div className="text-sm text-muted-foreground">
                    تسجيل خروج تلقائي بعد فترة عدم نشاط
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  30 دقيقة
                </Button>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">نسخ احتياطية</label>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    إنشاء نسخة احتياطية
                  </Button>
                  <Button variant="outline" size="sm">
                    جدولة النسخ
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* System Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 ml-2" />
                إعدادات النظام
              </CardTitle>
              <CardDescription>
                إعدادات عامة للنظام
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">اللغة</label>
                <Button variant="outline" className="w-full justify-start">
                  <Globe className="ml-2 h-4 w-4" />
                  العربية
                </Button>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">المنطقة الزمنية</label>
                <Button variant="outline" className="w-full justify-start">
                  (GMT+2) القاهرة
                </Button>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">العملة الافتراضية</label>
                <Button variant="outline" className="w-full justify-start">
                  جنيه مصري (EGP)
                </Button>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">تنسيق التاريخ</label>
                <Button variant="outline" className="w-full justify-start">
                  DD/MM/YYYY
                </Button>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">عدد العناصر في الصفحة</label>
                <Input defaultValue="10" type="number" />
              </div>
            </CardContent>
          </Card>

          {/* Theme Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Palette className="h-5 w-5 ml-2" />
                إعدادات المظهر
              </CardTitle>
              <CardDescription>
                تخصيص مظهر النظام
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">المظهر</label>
                <div className="grid grid-cols-3 gap-2">
                  <Button variant="outline" size="sm">
                    فاتح
                  </Button>
                  <Button variant="outline" size="sm">
                    داكن
                  </Button>
                  <Button variant="default" size="sm">
                    تلقائي
                  </Button>
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">اللون الأساسي</label>
                <div className="flex space-x-2">
                  <div className="w-8 h-8 rounded-full bg-blue-500 border-2 border-blue-600"></div>
                  <div className="w-8 h-8 rounded-full bg-green-500"></div>
                  <div className="w-8 h-8 rounded-full bg-purple-500"></div>
                  <div className="w-8 h-8 rounded-full bg-red-500"></div>
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">حجم الخط</label>
                <div className="grid grid-cols-3 gap-2">
                  <Button variant="outline" size="sm">
                    صغير
                  </Button>
                  <Button variant="default" size="sm">
                    متوسط
                  </Button>
                  <Button variant="outline" size="sm">
                    كبير
                  </Button>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">الشريط الجانبي المضغوط</div>
                  <div className="text-sm text-muted-foreground">
                    عرض الشريط الجانبي بشكل مضغوط
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  معطل
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Database & Backup */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 ml-2" />
              قاعدة البيانات والنسخ الاحتياطية
            </CardTitle>
            <CardDescription>
              إدارة قاعدة البيانات والنسخ الاحتياطية
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <h4 className="font-medium">معلومات قاعدة البيانات</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>حجم قاعدة البيانات:</span>
                    <span className="font-medium">245 MB</span>
                  </div>
                  <div className="flex justify-between">
                    <span>عدد الجداول:</span>
                    <span className="font-medium">28</span>
                  </div>
                  <div className="flex justify-between">
                    <span>آخر تحديث:</span>
                    <span className="font-medium">منذ 5 دقائق</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">النسخ الاحتياطية</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>آخر نسخة احتياطية:</span>
                    <span className="font-medium">اليوم 03:00</span>
                  </div>
                  <div className="flex justify-between">
                    <span>عدد النسخ المحفوظة:</span>
                    <span className="font-medium">7</span>
                  </div>
                  <div className="flex justify-between">
                    <span>حجم النسخ:</span>
                    <span className="font-medium">1.2 GB</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">إجراءات</h4>
                <div className="space-y-2">
                  <Button variant="outline" size="sm" className="w-full">
                    إنشاء نسخة احتياطية الآن
                  </Button>
                  <Button variant="outline" size="sm" className="w-full">
                    استعادة من نسخة احتياطية
                  </Button>
                  <Button variant="outline" size="sm" className="w-full">
                    تحسين قاعدة البيانات
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
