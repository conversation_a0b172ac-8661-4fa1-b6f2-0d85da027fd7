"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/customers/page",{

/***/ "(app-pages-browser)/./src/app/customers/page.tsx":
/*!************************************!*\
  !*** ./src/app/customers/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CustomersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./src/components/ui/data-table.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Mail,MapPin,Phone,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Mail,MapPin,Phone,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Mail,MapPin,Phone,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Mail,MapPin,Phone,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Mail,MapPin,Phone,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Mail,MapPin,Phone,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n// Sample customer data\nconst customers = [\n    {\n        id: \"CUST-001\",\n        name: \"أحمد محمد علي\",\n        email: \"<EMAIL>\",\n        phone: \"+20 ************\",\n        company: \"شركة النور للتجارة\",\n        address: \"القاهرة، مصر الجديدة\",\n        totalOrders: 15,\n        totalSpent: 25430.50,\n        lastOrder: \"2024-01-15\",\n        status: \"نشط\",\n        joinDate: \"2023-06-15\"\n    },\n    {\n        id: \"CUST-002\",\n        name: \"فاطمة علي حسن\",\n        email: \"<EMAIL>\",\n        phone: \"+20 ************\",\n        company: \"مؤسسة الأمل\",\n        address: \"الجيزة، الدقي\",\n        totalOrders: 8,\n        totalSpent: 12750.00,\n        lastOrder: \"2024-01-12\",\n        status: \"نشط\",\n        joinDate: \"2023-08-20\"\n    },\n    {\n        id: \"CUST-003\",\n        name: \"محمد حسن إبراهيم\",\n        email: \"<EMAIL>\",\n        phone: \"+20 ************\",\n        company: \"شركة المستقبل\",\n        address: \"الإسكندرية، سموحة\",\n        totalOrders: 22,\n        totalSpent: 45200.75,\n        lastOrder: \"2024-01-14\",\n        status: \"نشط\",\n        joinDate: \"2023-03-10\"\n    },\n    {\n        id: \"CUST-004\",\n        name: \"سارة أحمد محمود\",\n        email: \"<EMAIL>\",\n        phone: \"+20 ************\",\n        company: \"مكتب الإبداع\",\n        address: \"القاهرة، المعادي\",\n        totalOrders: 5,\n        totalSpent: 8500.25,\n        lastOrder: \"2024-01-08\",\n        status: \"غير نشط\",\n        joinDate: \"2023-11-05\"\n    },\n    {\n        id: \"CUST-005\",\n        name: \"عمر خالد يوسف\",\n        email: \"<EMAIL>\",\n        phone: \"+20 ************\",\n        company: \"شركة التطوير الحديث\",\n        address: \"الجيزة، المهندسين\",\n        totalOrders: 18,\n        totalSpent: 32100.00,\n        lastOrder: \"2024-01-13\",\n        status: \"نشط\",\n        joinDate: \"2023-05-22\"\n    },\n    {\n        id: \"CUST-006\",\n        name: \"نورا سامي عبدالله\",\n        email: \"<EMAIL>\",\n        phone: \"+20 ************\",\n        company: \"مؤسسة الرؤية\",\n        address: \"القاهرة، النزهة\",\n        totalOrders: 12,\n        totalSpent: 19800.50,\n        lastOrder: \"2024-01-11\",\n        status: \"نشط\",\n        joinDate: \"2023-07-18\"\n    },\n    {\n        id: \"CUST-007\",\n        name: \"حسام محمد طه\",\n        email: \"<EMAIL>\",\n        phone: \"+20 ************\",\n        company: \"شركة الابتكار\",\n        address: \"الإسكندرية، المنتزه\",\n        totalOrders: 9,\n        totalSpent: 15600.75,\n        lastOrder: \"2024-01-09\",\n        status: \"نشط\",\n        joinDate: \"2023-09-12\"\n    },\n    {\n        id: \"CUST-008\",\n        name: \"مريم أحمد فتحي\",\n        email: \"<EMAIL>\",\n        phone: \"+20 ************\",\n        company: \"مكتب الإنجاز\",\n        address: \"القاهرة، مدينة نصر\",\n        totalOrders: 6,\n        totalSpent: 11200.00,\n        lastOrder: \"2024-01-07\",\n        status: \"غير نشط\",\n        joinDate: \"2023-10-30\"\n    }\n];\nconst customerColumns = [\n    {\n        key: \"id\",\n        title: \"رقم العميل\",\n        sortable: true,\n        width: \"120px\"\n    },\n    {\n        key: \"name\",\n        title: \"الاسم\",\n        sortable: true,\n        render: (value, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-medium\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: row.company\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, undefined)\n    },\n    {\n        key: \"email\",\n        title: \"البريد الإلكتروني\",\n        render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-4 w-4 ml-2 text-muted-foreground\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, undefined)\n    },\n    {\n        key: \"phone\",\n        title: \"الهاتف\",\n        render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-4 w-4 ml-2 text-muted-foreground\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, undefined)\n    },\n    {\n        key: \"address\",\n        title: \"العنوان\",\n        render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-4 w-4 ml-2 text-muted-foreground\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, undefined)\n    },\n    {\n        key: \"totalOrders\",\n        title: \"عدد الطلبات\",\n        sortable: true,\n        width: \"120px\"\n    },\n    {\n        key: \"totalSpent\",\n        title: \"إجمالي المشتريات\",\n        sortable: true,\n        render: (value)=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(value),\n        width: \"150px\"\n    },\n    {\n        key: \"lastOrder\",\n        title: \"آخر طلب\",\n        sortable: true,\n        render: (value)=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(value),\n        width: \"120px\"\n    },\n    {\n        key: \"status\",\n        title: \"الحالة\",\n        render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(value === \"نشط\" ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\"),\n                children: value\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, undefined),\n        width: \"100px\"\n    },\n    {\n        key: \"id\",\n        title: \"الإجراءات\",\n        render: (value, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined),\n        width: \"120px\"\n    }\n];\nfunction CustomersPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_1__.MainLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: \"إدارة العملاء\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"إدارة بيانات العملاء ومتابعة طلباتهم\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"ml-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this),\n                                \"إضافة عميل جديد\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 md:grid-cols-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"إجمالي العملاء\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: customers.length\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"+2 عميل جديد هذا الشهر\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"العملاء النشطون\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: customers.filter((c)=>c.status === \"نشط\").length\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                Math.round(customers.filter((c)=>c.status === \"نشط\").length / customers.length * 100),\n                                                \"% من إجمالي العملاء\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"متوسط الطلبات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: Math.round(customers.reduce((sum, c)=>sum + c.totalOrders, 0) / customers.length)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"طلب لكل عميل\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"متوسط الإنفاق\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(customers.reduce((sum, c)=>sum + c.totalSpent, 0) / customers.length)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"لكل عميل\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"قائمة العملاء\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: \"جميع العملاء المسجلين في النظام\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_3__.DataTable, {\n                                data: customers,\n                                columns: customerColumns,\n                                pageSize: 10,\n                                mobileCardRender: (customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: customer.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: customer.company\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(customer.status === \"نشط\" ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\"),\n                                                        children: customer.status\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-4 w-4 ml-2 text-muted-foreground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            customer.email\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-4 w-4 ml-2 text-muted-foreground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            customer.phone\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-4 w-4 ml-2 text-muted-foreground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            customer.address\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"الطلبات: \",\n                                                            customer.totalOrders\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"الإنفاق: \",\n                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(customer.totalSpent)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"ml-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            \"تعديل\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"ml-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            \"حذف\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, void 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n            lineNumber: 223,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, this);\n}\n_c = CustomersPage;\nvar _c;\n$RefreshReg$(_c, \"CustomersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/customers/page.tsx\n"));

/***/ })

});