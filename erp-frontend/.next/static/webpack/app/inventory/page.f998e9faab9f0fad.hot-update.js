"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/app/inventory/page.tsx":
/*!************************************!*\
  !*** ./src/app/inventory/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InventoryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./src/components/ui/data-table.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Package_Plus_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Package,Plus,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Package_Plus_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Package,Plus,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Package_Plus_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Package,Plus,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Package_Plus_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Package,Plus,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n// Sample inventory data\nconst inventory = [\n    {\n        id: \"PROD-001\",\n        name: \"لابتوب ديل XPS 13\",\n        category: \"أجهزة كمبيوتر\",\n        sku: \"DELL-XPS13-001\",\n        quantity: 25,\n        minStock: 10,\n        maxStock: 50,\n        unitPrice: 25000.00,\n        totalValue: 625000.00,\n        supplier: \"شركة التقنية المتقدمة\",\n        location: \"مخزن أ - رف 1\",\n        status: \"متوفر\",\n        lastUpdated: \"2024-01-15\"\n    },\n    {\n        id: \"PROD-002\",\n        name: \"طابعة HP LaserJet\",\n        category: \"طابعات\",\n        sku: \"HP-LJ-002\",\n        quantity: 5,\n        minStock: 8,\n        maxStock: 20,\n        unitPrice: 3500.00,\n        totalValue: 17500.00,\n        supplier: \"مؤسسة الطباعة الحديثة\",\n        location: \"مخزن ب - رف 3\",\n        status: \"مخزون منخفض\",\n        lastUpdated: \"2024-01-14\"\n    },\n    {\n        id: \"PROD-003\",\n        name: \"شاشة سامسونج 27 بوصة\",\n        category: \"شاشات\",\n        sku: \"SAM-MON27-003\",\n        quantity: 15,\n        minStock: 5,\n        maxStock: 30,\n        unitPrice: 4200.00,\n        totalValue: 63000.00,\n        supplier: \"شركة العرض المرئي\",\n        location: \"مخزن أ - رف 2\",\n        status: \"متوفر\",\n        lastUpdated: \"2024-01-13\"\n    },\n    {\n        id: \"PROD-004\",\n        name: \"كيبورد لوجيتك ميكانيكي\",\n        category: \"ملحقات\",\n        sku: \"LOG-KB-004\",\n        quantity: 0,\n        minStock: 10,\n        maxStock: 40,\n        unitPrice: 850.00,\n        totalValue: 0.00,\n        supplier: \"متجر الملحقات\",\n        location: \"مخزن ج - رف 1\",\n        status: \"نفد المخزون\",\n        lastUpdated: \"2024-01-12\"\n    },\n    {\n        id: \"PROD-005\",\n        name: \"ماوس لاسلكي\",\n        category: \"ملحقات\",\n        sku: \"WIRE-MOUSE-005\",\n        quantity: 35,\n        minStock: 15,\n        maxStock: 60,\n        unitPrice: 320.00,\n        totalValue: 11200.00,\n        supplier: \"متجر الملحقات\",\n        location: \"مخزن ج - رف 2\",\n        status: \"متوفر\",\n        lastUpdated: \"2024-01-11\"\n    },\n    {\n        id: \"PROD-006\",\n        name: \"هارد ديسك خارجي 1TB\",\n        category: \"تخزين\",\n        sku: \"EXT-HDD-1TB-006\",\n        quantity: 12,\n        minStock: 8,\n        maxStock: 25,\n        unitPrice: 1200.00,\n        totalValue: 14400.00,\n        supplier: \"شركة التخزين الرقمي\",\n        location: \"مخزن ب - رف 1\",\n        status: \"متوفر\",\n        lastUpdated: \"2024-01-10\"\n    }\n];\nconst inventoryColumns = [\n    {\n        key: \"sku\",\n        title: \"رمز المنتج\",\n        sortable: true,\n        width: \"120px\"\n    },\n    {\n        key: \"name\",\n        title: \"اسم المنتج\",\n        sortable: true,\n        render: (value, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-medium\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: row.category\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined)\n    },\n    {\n        key: \"quantity\",\n        title: \"الكمية\",\n        sortable: true,\n        render: (value, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-medium \".concat(value === 0 ? 'text-red-600' : value <= row.minStock ? 'text-yellow-600' : 'text-green-600'),\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, undefined),\n                    value <= row.minStock && value > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Package_Plus_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-4 w-4 text-yellow-500 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, undefined),\n                    value === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Package_Plus_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-4 w-4 text-red-500 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, undefined),\n        width: \"100px\"\n    },\n    {\n        key: \"minStock\",\n        title: \"الحد الأدنى\",\n        sortable: true,\n        width: \"100px\"\n    },\n    {\n        key: \"unitPrice\",\n        title: \"سعر الوحدة\",\n        sortable: true,\n        render: (value)=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(value),\n        width: \"120px\"\n    },\n    {\n        key: \"totalValue\",\n        title: \"القيمة الإجمالية\",\n        sortable: true,\n        render: (value)=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(value),\n        width: \"140px\"\n    },\n    {\n        key: \"supplier\",\n        title: \"المورد\",\n        width: \"150px\"\n    },\n    {\n        key: \"location\",\n        title: \"الموقع\",\n        width: \"120px\"\n    },\n    {\n        key: \"status\",\n        title: \"الحالة\",\n        render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(value === \"متوفر\" ? \"bg-green-100 text-green-800\" : value === \"مخزون منخفض\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                children: value\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, undefined),\n        width: \"120px\"\n    }\n];\nfunction InventoryPage() {\n    const totalValue = inventory.reduce((sum, item)=>sum + item.totalValue, 0);\n    const lowStockItems = inventory.filter((item)=>item.quantity <= item.minStock && item.quantity > 0);\n    const outOfStockItems = inventory.filter((item)=>item.quantity === 0);\n    const totalItems = inventory.reduce((sum, item)=>sum + item.quantity, 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_1__.MainLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: \"إدارة المخزون\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"متابعة وإدارة مخزون المنتجات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Package_Plus_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"ml-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this),\n                                \"إضافة منتج جديد\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 md:grid-cols-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"إجمالي المنتجات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Package_Plus_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: inventory.length\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"منتج مختلف\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"إجمالي الكمية\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Package_Plus_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: totalItems\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"قطعة في المخزون\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"قيمة المخزون\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Package_Plus_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(totalValue)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"القيمة الإجمالية\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"تنبيهات المخزون\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Package_Plus_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-red-600\",\n                                            children: outOfStockItems.length + lowStockItems.length\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"منتج يحتاج انتباه\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, this),\n                (lowStockItems.length > 0 || outOfStockItems.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Package_Plus_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5 text-yellow-500 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"تنبيهات المخزون\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: \"منتجات تحتاج إعادة تموين\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    outOfStockItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-red-800\",\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-red-600 mr-2\",\n                                                            children: \"- نفد المخزون\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: \"طلب تموين\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, item.id, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 19\n                                        }, this)),\n                                    lowStockItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-yellow-800\",\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-yellow-600 mr-2\",\n                                                            children: [\n                                                                \"- متبقي \",\n                                                                item.quantity,\n                                                                \" (الحد الأدنى: \",\n                                                                item.minStock,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: \"طلب تموين\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, item.id, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"قائمة المخزون\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: \"جميع المنتجات الموجودة في المخزون\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_3__.DataTable, {\n                                data: inventory,\n                                columns: inventoryColumns,\n                                pageSize: 10,\n                                mobileCardRender: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: item.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: item.category\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    \"SKU: \",\n                                                                    item.sku\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(item.status === \"متوفر\" ? \"bg-green-100 text-green-800\" : item.status === \"مخزون منخفض\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                        children: item.status\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-muted-foreground\",\n                                                                children: \"الكمية: \"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium \".concat(item.quantity === 0 ? 'text-red-600' : item.quantity <= item.minStock ? 'text-yellow-600' : 'text-green-600'),\n                                                                children: item.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-muted-foreground\",\n                                                                children: \"الحد الأدنى: \"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: item.minStock\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-muted-foreground\",\n                                                                children: \"سعر الوحدة: \"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(item.unitPrice)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-muted-foreground\",\n                                                                children: \"القيمة: \"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(item.totalValue)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: [\n                                                            \"المورد: \",\n                                                            item.supplier\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: [\n                                                            \"الموقع: \",\n                                                            item.location\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 17\n                                    }, void 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n            lineNumber: 204,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/inventory/page.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, this);\n}\n_c = InventoryPage;\nvar _c;\n$RefreshReg$(_c, \"InventoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/inventory/page.tsx\n"));

/***/ })

});