{"version": 3, "file": "oauth-types.d.ts", "sourceRoot": "", "sources": ["../src/providers/oauth-types.ts"], "names": [], "mappings": "AAEA,oBAAY,iBAAiB,GACzB,WAAW,GACX,OAAO,GACP,WAAW,GACX,OAAO,GACP,WAAW,GACX,cAAc,GACd,UAAU,GACV,WAAW,GACX,KAAK,GACL,aAAa,GACb,QAAQ,GACR,SAAS,GACT,UAAU,GACV,aAAa,GACb,SAAS,GACT,SAAS,GACT,yBAAyB,GACzB,OAAO,GACP,WAAW,GACX,UAAU,GACV,QAAQ,GACR,YAAY,GACZ,YAAY,GACZ,YAAY,GACZ,QAAQ,GACR,QAAQ,GACR,QAAQ,GACR,SAAS,GACT,kBAAkB,GAClB,OAAO,GACP,WAAW,GACX,OAAO,GACP,UAAU,GACV,MAAM,GACN,UAAU,GACV,WAAW,GACX,QAAQ,GACR,QAAQ,GACR,OAAO,GACP,SAAS,GACT,aAAa,GACb,OAAO,GACP,MAAM,GACN,UAAU,GACV,MAAM,GACN,KAAK,GACL,SAAS,GACT,SAAS,GACT,WAAW,GACX,WAAW,GACX,QAAQ,GACR,YAAY,GACZ,OAAO,GACP,SAAS,GACT,QAAQ,GACR,SAAS,GACT,OAAO,GACP,QAAQ,GACR,SAAS,GACT,gBAAgB,GAChB,IAAI,GACJ,WAAW,GACX,WAAW,GACX,QAAQ,GACR,QAAQ,GACR,SAAS,GACT,MAAM,GACN,MAAM,CAAA"}