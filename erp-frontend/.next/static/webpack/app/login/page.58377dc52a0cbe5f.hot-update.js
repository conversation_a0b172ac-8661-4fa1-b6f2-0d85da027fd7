"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./src/components/auth/login-form.tsx":
/*!********************************************!*\
  !*** ./src/components/auth/login-form.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoginForm: () => (/* binding */ LoginForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* __next_internal_client_entry_do_not_use__ LoginForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction LoginForm(param) {\n    let { onLogin } = param;\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            const success = await onLogin(email, password);\n            if (success) {\n                router.push(\"/dashboard\");\n            } else {\n                setError(\"البريد الإلكتروني أو كلمة المرور غير صحيحة\");\n            }\n        } catch (error) {\n            setError(\"حدث خطأ أثناء تسجيل الدخول\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const demoAccounts = [\n        {\n            email: \"<EMAIL>\",\n            password: \"admin123\",\n            role: \"مدير النظام\",\n            color: \"bg-red-500\",\n            bgColor: \"bg-red-50\",\n            textColor: \"text-red-700\",\n            borderColor: \"border-red-200\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"manager123\",\n            role: \"مدير المبيعات\",\n            color: \"bg-blue-500\",\n            bgColor: \"bg-blue-50\",\n            textColor: \"text-blue-700\",\n            borderColor: \"border-blue-200\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"employee123\",\n            role: \"موظفة مبيعات\",\n            color: \"bg-green-500\",\n            bgColor: \"bg-green-50\",\n            textColor: \"text-green-700\",\n            borderColor: \"border-green-200\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"warehouse123\",\n            role: \"مسؤول المخزون\",\n            color: \"bg-yellow-500\",\n            bgColor: \"bg-yellow-50\",\n            textColor: \"text-yellow-700\",\n            borderColor: \"border-yellow-200\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"viewer123\",\n            role: \"مراجعة\",\n            color: \"bg-purple-500\",\n            bgColor: \"bg-purple-50\",\n            textColor: \"text-purple-700\",\n            borderColor: \"border-purple-200\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"accountant123\",\n            role: \"محاسب\",\n            color: \"bg-orange-500\",\n            bgColor: \"bg-orange-50\",\n            textColor: \"text-orange-700\",\n            borderColor: \"border-orange-200\"\n        }\n    ];\n    const fillDemoAccount = (demoEmail, demoPassword)=>{\n        setEmail(demoEmail);\n        setPassword(demoPassword);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-background p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-6xl grid md:grid-cols-2 gap-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"w-full max-w-md mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-primary rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-8 w-8 text-primary-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-2xl font-bold\",\n                                    children: \"تسجيل الدخول\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"ادخل بياناتك للوصول إلى نظام ERP\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"البريد الإلكتروني\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        type: \"email\",\n                                                        value: email,\n                                                        onChange: (e)=>setEmail(e.target.value),\n                                                        placeholder: \"<EMAIL>\",\n                                                        className: \"pr-10\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"كلمة المرور\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        type: showPassword ? \"text\" : \"password\",\n                                                        value: password,\n                                                        onChange: (e)=>setPassword(e.target.value),\n                                                        placeholder: \"••••••••\",\n                                                        className: \"pr-10 pl-10\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setShowPassword(!showPassword),\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2\",\n                                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-red-600 bg-red-50 p-3 rounded-md\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"submit\",\n                                        className: \"w-full\",\n                                        disabled: isLoading,\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"جاري تسجيل الدخول...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"ml-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"تسجيل الدخول\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-xl font-bold\",\n                                    children: \"حسابات التجربة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"اختر أي حساب للتجربة - كل حساب له صلاحيات مختلفة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: demoAccounts.map((account, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 rounded-lg border transition-all duration-200 hover:shadow-md \".concat(account.bgColor, \" \").concat(account.borderColor),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-4 rounded-full \".concat(account.color, \" shadow-sm\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium \".concat(account.textColor),\n                                                                    children: account.role\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                                    lineNumber: 155,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: account.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>fillDemoAccount(account.email, account.password),\n                                                    className: \"\".concat(account.borderColor, \" \").concat(account.textColor, \" hover:\").concat(account.bgColor),\n                                                    children: \"استخدام\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 p-4 bg-muted/50 rounded-lg border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-foreground mb-2\",\n                                            children: \"معلومات الصلاحيات:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-muted-foreground space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            className: \"text-foreground\",\n                                                            children: \"مدير النظام:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" صلاحيات كاملة لجميع الأقسام\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            className: \"text-foreground\",\n                                                            children: \"مدير المبيعات:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" إدارة العملاء والمبيعات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            className: \"text-foreground\",\n                                                            children: \"موظفة مبيعات:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" إضافة وتعديل العملاء والطلبات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            className: \"text-foreground\",\n                                                            children: \"مسؤول المخزون:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" إدارة المنتجات والمخزون\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            className: \"text-foreground\",\n                                                            children: \"مراجعة:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" عرض البيانات فقط\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            className: \"text-foreground\",\n                                                            children: \"محاسب:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" التقارير المالية والمبيعات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-4 bg-primary/5 rounded-lg border border-primary/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-primary mb-2\",\n                                            children: \"روابط سريعة:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-2 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/login\",\n                                                    className: \"text-primary hover:underline\",\n                                                    children: \"\\uD83D\\uDD10 صفحة تسجيل الدخول\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/dashboard\",\n                                                    className: \"text-primary hover:underline\",\n                                                    children: \"\\uD83D\\uDCCA لوحة التحكم\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/customers\",\n                                                    className: \"text-primary hover:underline\",\n                                                    children: \"\\uD83D\\uDC65 إدارة العملاء\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/inventory\",\n                                                    className: \"text-primary hover:underline\",\n                                                    children: \"\\uD83D\\uDCE6 إدارة المخزون\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginForm, \"QjaKSJEVQp1wB0abSM6uGwqVdw4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = LoginForm;\nvar _c;\n$RefreshReg$(_c, \"LoginForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/login-form.tsx\n"));

/***/ })

});