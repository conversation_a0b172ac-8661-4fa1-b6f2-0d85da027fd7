"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { useAuth } from "@/contexts/auth-context"
import { canAccessModule } from "@/lib/auth"
import {
  LayoutDashboard,
  Users,
  Package,
  ShoppingCart,
  FileText,
  BarChart3,
  Settings,
  Building2,
  Wallet,
  UserCheck,
  Truck,
  Calendar,
  MessageSquare,
  Shield,
} from "lucide-react"

const sidebarItems = [
  {
    title: "لوحة التحكم",
    href: "/dashboard",
    icon: LayoutDashboard,
    module: "dashboard",
  },
  {
    title: "إدارة العملاء",
    href: "/customers",
    icon: Users,
    module: "customers",
  },
  {
    title: "إدارة المخزون",
    href: "/inventory",
    icon: Package,
    module: "inventory",
  },
  {
    title: "المبيعات",
    href: "/sales",
    icon: ShoppingCart,
    module: "sales",
  },
  {
    title: "المشتريات",
    href: "/purchases",
    icon: Truck,
    module: "purchases",
  },
  {
    title: "الفواتير",
    href: "/invoices",
    icon: FileText,
    module: "invoices",
  },
  {
    title: "الحسابات",
    href: "/accounts",
    icon: Wallet,
    module: "accounts",
  },
  {
    title: "الموظفين",
    href: "/employees",
    icon: UserCheck,
    module: "employees",
  },
  {
    title: "المستخدمين",
    href: "/users",
    icon: Shield,
    module: "users",
  },
  {
    title: "المواعيد",
    href: "/appointments",
    icon: Calendar,
    module: "appointments",
  },
  {
    title: "الرسائل",
    href: "/messages",
    icon: MessageSquare,
    module: "messages",
  },
  {
    title: "التقارير",
    href: "/reports",
    icon: BarChart3,
    module: "reports",
  },
  {
    title: "الشركة",
    href: "/company",
    icon: Building2,
    module: "company",
  },
  {
    title: "الإعدادات",
    href: "/settings",
    icon: Settings,
    module: "settings",
  },
]

interface SidebarProps {
  className?: string
}

export function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname()
  const { user } = useAuth()

  // Filter items based on user permissions
  const visibleItems = sidebarItems.filter(item => {
    if (!user) return false
    return canAccessModule(user, item.module)
  })

  return (
    <div className={cn("pb-12 min-h-screen", className)}>
      <div className="space-y-4 py-4">
        <div className="px-3 py-2">
          <div className="flex items-center mb-6">
            <Building2 className="h-8 w-8 ml-2" />
            <h2 className="text-lg font-semibold">نظام ERP</h2>
          </div>

          {/* User info */}
          {user && (
            <div className="mb-6 p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${
                  user.role === 'admin' ? 'bg-red-500' :
                  user.role === 'manager' ? 'bg-blue-500' :
                  user.role === 'employee' ? 'bg-green-500' :
                  'bg-purple-500'
                }`}></div>
                <div className="text-sm">
                  <div className="font-medium truncate">{user.name}</div>
                  <div className="text-xs text-muted-foreground">{user.department}</div>
                </div>
              </div>
            </div>
          )}

          <div className="space-y-1">
            {visibleItems.map((item) => (
              <Button
                key={item.href}
                variant={pathname === item.href ? "secondary" : "ghost"}
                className={cn(
                  "w-full justify-start",
                  pathname === item.href && "bg-muted font-medium"
                )}
                asChild
              >
                <Link href={item.href}>
                  <item.icon className="ml-2 h-4 w-4" />
                  {item.title}
                </Link>
              </Button>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
