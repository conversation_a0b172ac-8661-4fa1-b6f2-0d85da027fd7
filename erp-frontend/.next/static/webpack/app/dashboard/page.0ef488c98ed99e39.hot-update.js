"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_auth_protected_route__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/protected-route */ \"(app-pages-browser)/./src/components/auth/protected-route.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./src/components/ui/data-table.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n// Sample data for demonstration\nconst recentOrders = [\n    {\n        id: \"ORD-001\",\n        customer: \"أحمد محمد\",\n        amount: 1250.00,\n        status: \"مكتمل\",\n        date: \"2024-01-15\"\n    },\n    {\n        id: \"ORD-002\",\n        customer: \"فاطمة علي\",\n        amount: 850.00,\n        status: \"قيد المعالجة\",\n        date: \"2024-01-14\"\n    },\n    {\n        id: \"ORD-003\",\n        customer: \"محمد حسن\",\n        amount: 2100.00,\n        status: \"مكتمل\",\n        date: \"2024-01-13\"\n    },\n    {\n        id: \"ORD-004\",\n        customer: \"سارة أحمد\",\n        amount: 750.00,\n        status: \"ملغي\",\n        date: \"2024-01-12\"\n    },\n    {\n        id: \"ORD-005\",\n        customer: \"عمر خالد\",\n        amount: 1500.00,\n        status: \"مكتمل\",\n        date: \"2024-01-11\"\n    }\n];\nconst orderColumns = [\n    {\n        key: \"id\",\n        title: \"رقم الطلب\",\n        sortable: true\n    },\n    {\n        key: \"customer\",\n        title: \"العميل\",\n        sortable: true\n    },\n    {\n        key: \"amount\",\n        title: \"المبلغ\",\n        sortable: true,\n        render: (value)=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(value)\n    },\n    {\n        key: \"status\",\n        title: \"الحالة\",\n        render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(value === \"مكتمل\" ? \"bg-green-100 text-green-800\" : value === \"قيد المعالجة\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                children: value\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined)\n    },\n    {\n        key: \"date\",\n        title: \"التاريخ\",\n        sortable: true,\n        render: (value)=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(value)\n    }\n];\nconst stats = [\n    {\n        title: \"إجمالي المبيعات\",\n        value: \"125,430\",\n        unit: \"ج.م\",\n        change: \"+12.5%\",\n        trend: \"up\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        title: \"عدد العملاء\",\n        value: \"1,234\",\n        unit: \"عميل\",\n        change: \"+8.2%\",\n        trend: \"up\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        title: \"المنتجات\",\n        value: \"856\",\n        unit: \"منتج\",\n        change: \"-2.1%\",\n        trend: \"down\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        title: \"الطلبات\",\n        value: \"342\",\n        unit: \"طلب\",\n        change: \"+15.3%\",\n        trend: \"up\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    }\n];\nconst alerts = [\n    {\n        type: \"warning\",\n        message: \"مخزون منخفض: 5 منتجات تحتاج إعادة تموين\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        type: \"success\",\n        message: \"تم تحديث أسعار 15 منتج بنجاح\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        type: \"warning\",\n        message: \"3 فواتير متأخرة السداد\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    }\n];\nfunction DashboardPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_protected_route__WEBPACK_IMPORTED_MODULE_2__.ProtectedRoute, {\n        module: \"dashboard\",\n        action: \"read\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_1__.MainLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold\",\n                                children: \"لوحة التحكم\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"مرحباً بك في نظام إدارة الموارد المؤسسية\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-4\",\n                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: stat.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    stat.value,\n                                                    \" \",\n                                                    stat.unit\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground flex items-center\",\n                                                children: [\n                                                    stat.trend === \"up\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-3 w-3 text-green-500 ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-3 w-3 text-red-500 ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    stat.change,\n                                                    \" من الشهر الماضي\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        children: \"التنبيهات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"التنبيهات والإشعارات المهمة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: alerts.map((alert, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center p-3 rounded-lg \".concat(alert.type === \"warning\" ? \"bg-yellow-50 border border-yellow-200\" : \"bg-green-50 border border-green-200\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(alert.icon, {\n                                                    className: \"h-5 w-5 ml-3 \".concat(alert.type === \"warning\" ? \"text-yellow-600\" : \"text-green-600\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: alert.message\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        children: \"الطلبات الأخيرة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"آخر الطلبات المسجلة في النظام\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_4__.DataTable, {\n                                    data: recentOrders,\n                                    columns: orderColumns,\n                                    pageSize: 5,\n                                    mobileCardRender: (order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: order.id\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(order.status === \"مكتمل\" ? \"bg-green-100 text-green-800\" : order.status === \"قيد المعالجة\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                            children: order.status\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: [\n                                                        \"العميل: \",\n                                                        order.customer\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(order.amount)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(order.date)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n            lineNumber: 156,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});