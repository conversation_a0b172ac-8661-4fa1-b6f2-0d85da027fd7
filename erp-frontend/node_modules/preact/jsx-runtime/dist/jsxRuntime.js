var r=require("preact"),e=/["&<]/;function t(r){if(0===r.length||!1===e.test(r))return r;for(var t=0,n=0,o="",f="";n<r.length;n++){switch(r.charCodeAt(n)){case 34:f="&quot;";break;case 38:f="&amp;";break;case 60:f="&lt;";break;default:continue}n!==t&&(o+=r.slice(t,n)),o+=f,t=n+1}return n!==t&&(o+=r.slice(t,n)),o}var n=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,o=0,f=Array.isArray;function u(e,t,n,f,u,i){t||(t={});var a,c,p=t;if("ref"in p)for(c in p={},t)"ref"==c?a=t[c]:p[c]=t[c];var l={type:e,props:p,key:n,ref:a,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:--o,__i:-1,__u:0,__source:u,__self:i};if("function"==typeof e&&(a=e.defaultProps))for(c in a)void 0===p[c]&&(p[c]=a[c]);return r.options.vnode&&r.options.vnode(l),l}var i={},a=/[A-Z]/g;Object.defineProperty(exports,"Fragment",{enumerable:!0,get:function(){return r.Fragment}}),exports.jsx=u,exports.jsxAttr=function(e,o){if(r.options.attr){var f=r.options.attr(e,o);if("string"==typeof f)return f}if("ref"===e||"key"===e)return"";if("style"===e&&"object"==typeof o){var u="";for(var c in o){var p=o[c];if(null!=p&&""!==p){var l="-"==c[0]?c:i[c]||(i[c]=c.replace(a,"-$&").toLowerCase()),s=";";"number"!=typeof p||l.startsWith("--")||n.test(l)||(s="px;"),u=u+l+":"+p+s}}return e+'="'+u+'"'}return null==o||!1===o||"function"==typeof o||"object"==typeof o?"":!0===o?e:e+'="'+t(o)+'"'},exports.jsxDEV=u,exports.jsxEscape=function r(e){if(null==e||"boolean"==typeof e||"function"==typeof e)return null;if("object"==typeof e){if(void 0===e.constructor)return e;if(f(e)){for(var n=0;n<e.length;n++)e[n]=r(e[n]);return e}}return t(""+e)},exports.jsxTemplate=function(e){var t=u(r.Fragment,{tpl:e,exprs:[].slice.call(arguments,1)});return t.key=t.__v,t},exports.jsxs=u;
//# sourceMappingURL=jsxRuntime.js.map
