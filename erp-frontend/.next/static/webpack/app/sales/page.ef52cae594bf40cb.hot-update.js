"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/sales/page",{

/***/ "(app-pages-browser)/./src/app/sales/page.tsx":
/*!********************************!*\
  !*** ./src/app/sales/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SalesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./src/components/ui/data-table.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Edit_Eye_Plus_ShoppingCart_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Edit,Eye,Plus,ShoppingCart,Trash2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Edit_Eye_Plus_ShoppingCart_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Edit,Eye,Plus,ShoppingCart,Trash2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Edit_Eye_Plus_ShoppingCart_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Edit,Eye,Plus,ShoppingCart,Trash2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Edit_Eye_Plus_ShoppingCart_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Edit,Eye,Plus,ShoppingCart,Trash2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Edit_Eye_Plus_ShoppingCart_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Edit,Eye,Plus,ShoppingCart,Trash2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Edit_Eye_Plus_ShoppingCart_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Edit,Eye,Plus,ShoppingCart,Trash2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Edit_Eye_Plus_ShoppingCart_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Edit,Eye,Plus,ShoppingCart,Trash2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Edit_Eye_Plus_ShoppingCart_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Edit,Eye,Plus,ShoppingCart,Trash2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n// Sample sales data\nconst sales = [\n    {\n        id: \"SALE-001\",\n        invoiceNumber: \"INV-2024-001\",\n        customer: \"أحمد محمد علي\",\n        customerEmail: \"<EMAIL>\",\n        items: [\n            {\n                name: \"لابتوب ديل XPS 13\",\n                quantity: 1,\n                price: 25000.00\n            },\n            {\n                name: \"ماوس لاسلكي\",\n                quantity: 2,\n                price: 320.00\n            }\n        ],\n        subtotal: 25640.00,\n        tax: 3846.00,\n        discount: 500.00,\n        total: 28986.00,\n        paymentMethod: \"نقدي\",\n        paymentStatus: \"مدفوع\",\n        status: \"مكتمل\",\n        salesPerson: \"محمد أحمد\",\n        date: \"2024-01-15\",\n        dueDate: \"2024-01-15\"\n    },\n    {\n        id: \"SALE-002\",\n        invoiceNumber: \"INV-2024-002\",\n        customer: \"فاطمة علي حسن\",\n        customerEmail: \"<EMAIL>\",\n        items: [\n            {\n                name: \"طابعة HP LaserJet\",\n                quantity: 1,\n                price: 3500.00\n            },\n            {\n                name: \"شاشة سامسونج 27 بوصة\",\n                quantity: 1,\n                price: 4200.00\n            }\n        ],\n        subtotal: 7700.00,\n        tax: 1155.00,\n        discount: 0.00,\n        total: 8855.00,\n        paymentMethod: \"بطاقة ائتمان\",\n        paymentStatus: \"مدفوع\",\n        status: \"مكتمل\",\n        salesPerson: \"سارة محمود\",\n        date: \"2024-01-14\",\n        dueDate: \"2024-01-14\"\n    },\n    {\n        id: \"SALE-003\",\n        invoiceNumber: \"INV-2024-003\",\n        customer: \"محمد حسن إبراهيم\",\n        customerEmail: \"<EMAIL>\",\n        items: [\n            {\n                name: \"هارد ديسك خارجي 1TB\",\n                quantity: 3,\n                price: 1200.00\n            },\n            {\n                name: \"كيبورد لوجيتك ميكانيكي\",\n                quantity: 2,\n                price: 850.00\n            }\n        ],\n        subtotal: 5300.00,\n        tax: 795.00,\n        discount: 200.00,\n        total: 5895.00,\n        paymentMethod: \"تحويل بنكي\",\n        paymentStatus: \"قيد الانتظار\",\n        status: \"قيد المعالجة\",\n        salesPerson: \"أحمد علي\",\n        date: \"2024-01-13\",\n        dueDate: \"2024-01-20\"\n    },\n    {\n        id: \"SALE-004\",\n        invoiceNumber: \"INV-2024-004\",\n        customer: \"سارة أحمد محمود\",\n        customerEmail: \"<EMAIL>\",\n        items: [\n            {\n                name: \"ماوس لاسلكي\",\n                quantity: 5,\n                price: 320.00\n            }\n        ],\n        subtotal: 1600.00,\n        tax: 240.00,\n        discount: 50.00,\n        total: 1790.00,\n        paymentMethod: \"نقدي\",\n        paymentStatus: \"مدفوع\",\n        status: \"مكتمل\",\n        salesPerson: \"نورا سامي\",\n        date: \"2024-01-12\",\n        dueDate: \"2024-01-12\"\n    },\n    {\n        id: \"SALE-005\",\n        invoiceNumber: \"INV-2024-005\",\n        customer: \"عمر خالد يوسف\",\n        customerEmail: \"<EMAIL>\",\n        items: [\n            {\n                name: \"لابتوب ديل XPS 13\",\n                quantity: 2,\n                price: 25000.00\n            },\n            {\n                name: \"شاشة سامسونج 27 بوصة\",\n                quantity: 2,\n                price: 4200.00\n            }\n        ],\n        subtotal: 58400.00,\n        tax: 8760.00,\n        discount: 1000.00,\n        total: 66160.00,\n        paymentMethod: \"شيك\",\n        paymentStatus: \"غير مدفوع\",\n        status: \"ملغي\",\n        salesPerson: \"حسام محمد\",\n        date: \"2024-01-11\",\n        dueDate: \"2024-01-25\"\n    }\n];\nconst salesColumns = [\n    {\n        key: \"invoiceNumber\",\n        title: \"رقم الفاتورة\",\n        sortable: true,\n        width: \"130px\"\n    },\n    {\n        key: \"customer\",\n        title: \"العميل\",\n        sortable: true,\n        render: (value, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-medium\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: row.customerEmail\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, undefined)\n    },\n    {\n        key: \"items\",\n        title: \"المنتجات\",\n        render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-xs\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: [\n                        value.slice(0, 2).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"truncate\",\n                                children: [\n                                    item.quantity,\n                                    \"x \",\n                                    item.name\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, undefined)),\n                        value.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-muted-foreground\",\n                            children: [\n                                \"+\",\n                                value.length - 2,\n                                \" منتج آخر\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, undefined),\n        width: \"200px\"\n    },\n    {\n        key: \"total\",\n        title: \"المبلغ الإجمالي\",\n        sortable: true,\n        render: (value)=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(value),\n        width: \"130px\"\n    },\n    {\n        key: \"paymentMethod\",\n        title: \"طريقة الدفع\",\n        width: \"120px\"\n    },\n    {\n        key: \"paymentStatus\",\n        title: \"حالة الدفع\",\n        render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(value === \"مدفوع\" ? \"bg-green-100 text-green-800\" : value === \"قيد الانتظار\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                children: value\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, undefined),\n        width: \"120px\"\n    },\n    {\n        key: \"status\",\n        title: \"الحالة\",\n        render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(value === \"مكتمل\" ? \"bg-green-100 text-green-800\" : value === \"قيد المعالجة\" ? \"bg-blue-100 text-blue-800\" : \"bg-red-100 text-red-800\"),\n                children: value\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, undefined),\n        width: \"120px\"\n    },\n    {\n        key: \"salesPerson\",\n        title: \"مندوب المبيعات\",\n        width: \"130px\"\n    },\n    {\n        key: \"date\",\n        title: \"التاريخ\",\n        sortable: true,\n        render: (value)=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(value),\n        width: \"120px\"\n    },\n    {\n        key: \"id\",\n        title: \"الإجراءات\",\n        render: (value, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Edit_Eye_Plus_ShoppingCart_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Edit_Eye_Plus_ShoppingCart_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Edit_Eye_Plus_ShoppingCart_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, undefined),\n        width: \"120px\"\n    }\n];\nfunction SalesPage() {\n    const totalSales = sales.reduce((sum, sale)=>sum + sale.total, 0);\n    const completedSales = sales.filter((sale)=>sale.status === \"مكتمل\");\n    const pendingSales = sales.filter((sale)=>sale.status === \"قيد المعالجة\");\n    const paidSales = sales.filter((sale)=>sale.paymentStatus === \"مدفوع\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_1__.MainLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: \"إدارة المبيعات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"متابعة وإدارة عمليات البيع والفواتير\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Edit_Eye_Plus_ShoppingCart_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"ml-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                \"إنشاء فاتورة جديدة\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 md:grid-cols-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"إجمالي المبيعات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Edit_Eye_Plus_ShoppingCart_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(totalSales)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"+12.5% من الشهر الماضي\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"عدد الفواتير\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Edit_Eye_Plus_ShoppingCart_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: sales.length\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"فاتورة هذا الشهر\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"المبيعات المكتملة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Edit_Eye_Plus_ShoppingCart_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: completedSales.length\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                Math.round(completedSales.length / sales.length * 100),\n                                                \"% من إجمالي الفواتير\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"المدفوعات المحصلة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Edit_Eye_Plus_ShoppingCart_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(paidSales.reduce((sum, sale)=>sum + sale.total, 0))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                paidSales.length,\n                                                \" فاتورة مدفوعة\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this),\n                pendingSales.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Edit_Eye_Plus_ShoppingCart_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-500 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"مبيعات قيد المعالجة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: \"فواتير تحتاج متابعة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: pendingSales.map((sale)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-blue-800\",\n                                                        children: sale.invoiceNumber\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-blue-600 mr-2\",\n                                                        children: [\n                                                            \"- \",\n                                                            sale.customer\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-blue-600\",\n                                                        children: [\n                                                            \"(\",\n                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(sale.total),\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: \"عرض التفاصيل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        size: \"sm\",\n                                                        children: \"تحديث الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, sale.id, true, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"قائمة المبيعات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: \"جميع فواتير المبيعات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_3__.DataTable, {\n                                data: sales,\n                                columns: salesColumns,\n                                pageSize: 10,\n                                mobileCardRender: (sale)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: sale.invoiceNumber\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: sale.customer\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(sale.total)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(sale.date)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(sale.paymentStatus === \"مدفوع\" ? \"bg-green-100 text-green-800\" : sale.paymentStatus === \"قيد الانتظار\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                        children: sale.paymentStatus\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(sale.status === \"مكتمل\" ? \"bg-green-100 text-green-800\" : sale.status === \"قيد المعالجة\" ? \"bg-blue-100 text-blue-800\" : \"bg-red-100 text-red-800\"),\n                                                        children: sale.status\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"طريقة الدفع: \",\n                                                            sale.paymentMethod\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"مندوب المبيعات: \",\n                                                            sale.salesPerson\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"عدد المنتجات: \",\n                                                            sale.items.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Edit_Eye_Plus_ShoppingCart_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"ml-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            \"عرض\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Edit_Eye_Plus_ShoppingCart_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"ml-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            \"تعديل\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 17\n                                    }, void 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n            lineNumber: 241,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/sales/page.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\n_c = SalesPage;\nvar _c;\n$RefreshReg$(_c, \"SalesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/sales/page.tsx\n"));

/***/ })

});