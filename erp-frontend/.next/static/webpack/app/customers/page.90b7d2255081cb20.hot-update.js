"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/customers/page",{

/***/ "(app-pages-browser)/./src/app/customers/page.tsx":
/*!************************************!*\
  !*** ./src/app/customers/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CustomersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_auth_protected_route__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/protected-route */ \"(app-pages-browser)/./src/components/auth/protected-route.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./src/components/ui/data-table.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Mail,MapPin,Phone,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Mail,MapPin,Phone,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Mail,MapPin,Phone,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Mail,MapPin,Phone,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Mail,MapPin,Phone,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Mail,MapPin,Phone,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// Sample customer data\nconst customers = [\n    {\n        id: \"CUST-001\",\n        name: \"أحمد محمد علي\",\n        email: \"<EMAIL>\",\n        phone: \"+20 ************\",\n        company: \"شركة النور للتجارة\",\n        address: \"القاهرة، مصر الجديدة\",\n        totalOrders: 15,\n        totalSpent: 25430.50,\n        lastOrder: \"2024-01-15\",\n        status: \"نشط\",\n        joinDate: \"2023-06-15\"\n    },\n    {\n        id: \"CUST-002\",\n        name: \"فاطمة علي حسن\",\n        email: \"<EMAIL>\",\n        phone: \"+20 ************\",\n        company: \"مؤسسة الأمل\",\n        address: \"الجيزة، الدقي\",\n        totalOrders: 8,\n        totalSpent: 12750.00,\n        lastOrder: \"2024-01-12\",\n        status: \"نشط\",\n        joinDate: \"2023-08-20\"\n    },\n    {\n        id: \"CUST-003\",\n        name: \"محمد حسن إبراهيم\",\n        email: \"<EMAIL>\",\n        phone: \"+20 ************\",\n        company: \"شركة المستقبل\",\n        address: \"الإسكندرية، سموحة\",\n        totalOrders: 22,\n        totalSpent: 45200.75,\n        lastOrder: \"2024-01-14\",\n        status: \"نشط\",\n        joinDate: \"2023-03-10\"\n    },\n    {\n        id: \"CUST-004\",\n        name: \"سارة أحمد محمود\",\n        email: \"<EMAIL>\",\n        phone: \"+20 ************\",\n        company: \"مكتب الإبداع\",\n        address: \"القاهرة، المعادي\",\n        totalOrders: 5,\n        totalSpent: 8500.25,\n        lastOrder: \"2024-01-08\",\n        status: \"غير نشط\",\n        joinDate: \"2023-11-05\"\n    },\n    {\n        id: \"CUST-005\",\n        name: \"عمر خالد يوسف\",\n        email: \"<EMAIL>\",\n        phone: \"+20 ************\",\n        company: \"شركة التطوير الحديث\",\n        address: \"الجيزة، المهندسين\",\n        totalOrders: 18,\n        totalSpent: 32100.00,\n        lastOrder: \"2024-01-13\",\n        status: \"نشط\",\n        joinDate: \"2023-05-22\"\n    },\n    {\n        id: \"CUST-006\",\n        name: \"نورا سامي عبدالله\",\n        email: \"<EMAIL>\",\n        phone: \"+20 ************\",\n        company: \"مؤسسة الرؤية\",\n        address: \"القاهرة، النزهة\",\n        totalOrders: 12,\n        totalSpent: 19800.50,\n        lastOrder: \"2024-01-11\",\n        status: \"نشط\",\n        joinDate: \"2023-07-18\"\n    },\n    {\n        id: \"CUST-007\",\n        name: \"حسام محمد طه\",\n        email: \"<EMAIL>\",\n        phone: \"+20 ************\",\n        company: \"شركة الابتكار\",\n        address: \"الإسكندرية، المنتزه\",\n        totalOrders: 9,\n        totalSpent: 15600.75,\n        lastOrder: \"2024-01-09\",\n        status: \"نشط\",\n        joinDate: \"2023-09-12\"\n    },\n    {\n        id: \"CUST-008\",\n        name: \"مريم أحمد فتحي\",\n        email: \"<EMAIL>\",\n        phone: \"+20 ************\",\n        company: \"مكتب الإنجاز\",\n        address: \"القاهرة، مدينة نصر\",\n        totalOrders: 6,\n        totalSpent: 11200.00,\n        lastOrder: \"2024-01-07\",\n        status: \"غير نشط\",\n        joinDate: \"2023-10-30\"\n    }\n];\nconst customerColumns = [\n    {\n        key: \"id\",\n        title: \"رقم العميل\",\n        sortable: true,\n        width: \"120px\"\n    },\n    {\n        key: \"name\",\n        title: \"الاسم\",\n        sortable: true,\n        render: (value, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-medium\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: row.company\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, undefined)\n    },\n    {\n        key: \"email\",\n        title: \"البريد الإلكتروني\",\n        render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-4 w-4 ml-2 text-muted-foreground\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, undefined)\n    },\n    {\n        key: \"phone\",\n        title: \"الهاتف\",\n        render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-4 w-4 ml-2 text-muted-foreground\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, undefined)\n    },\n    {\n        key: \"address\",\n        title: \"العنوان\",\n        render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"h-4 w-4 ml-2 text-muted-foreground\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, undefined)\n    },\n    {\n        key: \"totalOrders\",\n        title: \"عدد الطلبات\",\n        sortable: true,\n        width: \"120px\"\n    },\n    {\n        key: \"totalSpent\",\n        title: \"إجمالي المشتريات\",\n        sortable: true,\n        render: (value)=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(value),\n        width: \"150px\"\n    },\n    {\n        key: \"lastOrder\",\n        title: \"آخر طلب\",\n        sortable: true,\n        render: (value)=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(value),\n        width: \"120px\"\n    },\n    {\n        key: \"status\",\n        title: \"الحالة\",\n        render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(value === \"نشط\" ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\"),\n                children: value\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, undefined),\n        width: \"100px\"\n    },\n    {\n        key: \"id\",\n        title: \"الإجراءات\",\n        render: (value, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined),\n        width: \"120px\"\n    }\n];\nfunction CustomersPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_protected_route__WEBPACK_IMPORTED_MODULE_2__.ProtectedRoute, {\n        module: \"customers\",\n        action: \"read\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_1__.MainLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold\",\n                                        children: \"إدارة العملاء\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"إدارة بيانات العملاء ومتابعة طلباتهم\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_protected_route__WEBPACK_IMPORTED_MODULE_2__.PermissionGuard, {\n                                module: \"customers\",\n                                action: \"write\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"ml-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إضافة عميل جديد\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4 md:grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"إجمالي العملاء\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: customers.length\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: \"+2 عميل جديد هذا الشهر\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"العملاء النشطون\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: customers.filter((c)=>c.status === \"نشط\").length\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: [\n                                                    Math.round(customers.filter((c)=>c.status === \"نشط\").length / customers.length * 100),\n                                                    \"% من إجمالي العملاء\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"متوسط الطلبات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: Math.round(customers.reduce((sum, c)=>sum + c.totalOrders, 0) / customers.length)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: \"طلب لكل عميل\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"متوسط الإنفاق\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(customers.reduce((sum, c)=>sum + c.totalSpent, 0) / customers.length)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: \"لكل عميل\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        children: \"قائمة العملاء\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"جميع العملاء المسجلين في النظام\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_4__.DataTable, {\n                                    data: customers,\n                                    columns: customerColumns,\n                                    pageSize: 10,\n                                    mobileCardRender: (customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: customer.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 23\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: customer.company\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                                    lineNumber: 321,\n                                                                    columnNumber: 23\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(customer.status === \"نشط\" ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\"),\n                                                            children: customer.status\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 19\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 23\n                                                                }, void 0),\n                                                                customer.email\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 23\n                                                                }, void 0),\n                                                                customer.phone\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 23\n                                                                }, void 0),\n                                                                customer.address\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 19\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"الطلبات: \",\n                                                                customer.totalOrders\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"الإنفاق: \",\n                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(customer.totalSpent)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 19\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"ml-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 23\n                                                                }, void 0),\n                                                                \"تعديل\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_MapPin_Phone_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"ml-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                                    lineNumber: 357,\n                                                                    columnNumber: 23\n                                                                }, void 0),\n                                                                \"حذف\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 19\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n                lineNumber: 224,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n            lineNumber: 223,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/customers/page.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, this);\n}\n_c = CustomersPage;\nvar _c;\n$RefreshReg$(_c, \"CustomersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/customers/page.tsx\n"));

/***/ })

});