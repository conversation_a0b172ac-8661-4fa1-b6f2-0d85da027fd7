"use client"

import { MainLayout } from "@/components/layout/main-layout"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DataTable, Column } from "@/components/ui/data-table"
import { formatCurrency, formatDate } from "@/lib/utils"
import {
  TrendingUp,
  TrendingDown,
  Users,
  Package,
  ShoppingCart,
  DollarSign,
  AlertTriangle,
  CheckCircle,
} from "lucide-react"

// Sample data for demonstration
const recentOrders = [
  {
    id: "ORD-001",
    customer: "أحمد محمد",
    amount: 1250.00,
    status: "مكتمل",
    date: "2024-01-15",
  },
  {
    id: "ORD-002",
    customer: "فاطمة علي",
    amount: 850.00,
    status: "قيد المعالجة",
    date: "2024-01-14",
  },
  {
    id: "ORD-003",
    customer: "محمد حسن",
    amount: 2100.00,
    status: "مكتمل",
    date: "2024-01-13",
  },
  {
    id: "ORD-004",
    customer: "سارة أحمد",
    amount: 750.00,
    status: "ملغي",
    date: "2024-01-12",
  },
  {
    id: "ORD-005",
    customer: "عمر خالد",
    amount: 1500.00,
    status: "مكتمل",
    date: "2024-01-11",
  },
]

const orderColumns: Column<typeof recentOrders[0]>[] = [
  {
    key: "id",
    title: "رقم الطلب",
    sortable: true,
  },
  {
    key: "customer",
    title: "العميل",
    sortable: true,
  },
  {
    key: "amount",
    title: "المبلغ",
    sortable: true,
    render: (value) => formatCurrency(value),
  },
  {
    key: "status",
    title: "الحالة",
    render: (value) => (
      <span
        className={`px-2 py-1 rounded-full text-xs font-medium ${
          value === "مكتمل"
            ? "bg-green-100 text-green-800"
            : value === "قيد المعالجة"
            ? "bg-yellow-100 text-yellow-800"
            : "bg-red-100 text-red-800"
        }`}
      >
        {value}
      </span>
    ),
  },
  {
    key: "date",
    title: "التاريخ",
    sortable: true,
    render: (value) => formatDate(value),
  },
]

const stats = [
  {
    title: "إجمالي المبيعات",
    value: "125,430",
    unit: "ج.م",
    change: "+12.5%",
    trend: "up",
    icon: DollarSign,
  },
  {
    title: "عدد العملاء",
    value: "1,234",
    unit: "عميل",
    change: "+8.2%",
    trend: "up",
    icon: Users,
  },
  {
    title: "المنتجات",
    value: "856",
    unit: "منتج",
    change: "-2.1%",
    trend: "down",
    icon: Package,
  },
  {
    title: "الطلبات",
    value: "342",
    unit: "طلب",
    change: "+15.3%",
    trend: "up",
    icon: ShoppingCart,
  },
]

const alerts = [
  {
    type: "warning",
    message: "مخزون منخفض: 5 منتجات تحتاج إعادة تموين",
    icon: AlertTriangle,
  },
  {
    type: "success",
    message: "تم تحديث أسعار 15 منتج بنجاح",
    icon: CheckCircle,
  },
  {
    type: "warning",
    message: "3 فواتير متأخرة السداد",
    icon: AlertTriangle,
  },
]

export default function DashboardPage() {
  return (
    <ProtectedRoute module="dashboard" action="read">
      <MainLayout>
        <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-3xl font-bold">لوحة التحكم</h1>
          <p className="text-muted-foreground">
            مرحباً بك في نظام إدارة الموارد المؤسسية
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <stat.icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stat.value} {stat.unit}
                </div>
                <p className="text-xs text-muted-foreground flex items-center">
                  {stat.trend === "up" ? (
                    <TrendingUp className="h-3 w-3 text-green-500 ml-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-500 ml-1" />
                  )}
                  {stat.change} من الشهر الماضي
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Alerts */}
        <Card>
          <CardHeader>
            <CardTitle>التنبيهات</CardTitle>
            <CardDescription>
              التنبيهات والإشعارات المهمة
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {alerts.map((alert, index) => (
                <div
                  key={index}
                  className={`flex items-center p-3 rounded-lg ${
                    alert.type === "warning"
                      ? "bg-yellow-50 border border-yellow-200"
                      : "bg-green-50 border border-green-200"
                  }`}
                >
                  <alert.icon
                    className={`h-5 w-5 ml-3 ${
                      alert.type === "warning"
                        ? "text-yellow-600"
                        : "text-green-600"
                    }`}
                  />
                  <span className="text-sm">{alert.message}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Orders */}
        <Card>
          <CardHeader>
            <CardTitle>الطلبات الأخيرة</CardTitle>
            <CardDescription>
              آخر الطلبات المسجلة في النظام
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DataTable
              data={recentOrders}
              columns={orderColumns}
              pageSize={5}
              mobileCardRender={(order) => (
                <div className="p-4 space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">{order.id}</span>
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        order.status === "مكتمل"
                          ? "bg-green-100 text-green-800"
                          : order.status === "قيد المعالجة"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {order.status}
                    </span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    العميل: {order.customer}
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">
                      {formatCurrency(order.amount)}
                    </span>
                    <span className="text-sm text-muted-foreground">
                      {formatDate(order.date)}
                    </span>
                  </div>
                </div>
              )}
            />
          </CardContent>
        </Card>
        </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
