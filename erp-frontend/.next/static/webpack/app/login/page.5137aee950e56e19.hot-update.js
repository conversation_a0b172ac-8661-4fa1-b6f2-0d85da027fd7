"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./src/components/auth/login-form.tsx":
/*!********************************************!*\
  !*** ./src/components/auth/login-form.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoginForm: () => (/* binding */ LoginForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* __next_internal_client_entry_do_not_use__ LoginForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction LoginForm(param) {\n    let { onLogin } = param;\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            const success = await onLogin(email, password);\n            if (success) {\n                router.push(\"/dashboard\");\n            } else {\n                setError(\"البريد الإلكتروني أو كلمة المرور غير صحيحة\");\n            }\n        } catch (error) {\n            setError(\"حدث خطأ أثناء تسجيل الدخول\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const demoAccounts = [\n        {\n            email: \"<EMAIL>\",\n            password: \"admin123\",\n            role: \"مدير النظام\",\n            color: \"bg-red-500\",\n            bgColor: \"bg-red-50\",\n            textColor: \"text-red-700\",\n            borderColor: \"border-red-200\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"manager123\",\n            role: \"مدير المبيعات\",\n            color: \"bg-blue-500\",\n            bgColor: \"bg-blue-50\",\n            textColor: \"text-blue-700\",\n            borderColor: \"border-blue-200\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"employee123\",\n            role: \"موظفة مبيعات\",\n            color: \"bg-green-500\",\n            bgColor: \"bg-green-50\",\n            textColor: \"text-green-700\",\n            borderColor: \"border-green-200\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"warehouse123\",\n            role: \"مسؤول المخزون\",\n            color: \"bg-yellow-500\",\n            bgColor: \"bg-yellow-50\",\n            textColor: \"text-yellow-700\",\n            borderColor: \"border-yellow-200\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"viewer123\",\n            role: \"مراجعة\",\n            color: \"bg-purple-500\",\n            bgColor: \"bg-purple-50\",\n            textColor: \"text-purple-700\",\n            borderColor: \"border-purple-200\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"accountant123\",\n            role: \"محاسب\",\n            color: \"bg-orange-500\",\n            bgColor: \"bg-orange-50\",\n            textColor: \"text-orange-700\",\n            borderColor: \"border-orange-200\"\n        }\n    ];\n    const fillDemoAccount = (demoEmail, demoPassword)=>{\n        setEmail(demoEmail);\n        setPassword(demoPassword);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-background p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-6xl grid md:grid-cols-2 gap-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"w-full max-w-md mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-primary rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-8 w-8 text-primary-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-2xl font-bold\",\n                                    children: \"تسجيل الدخول\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"ادخل بياناتك للوصول إلى نظام ERP\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"البريد الإلكتروني\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        type: \"email\",\n                                                        value: email,\n                                                        onChange: (e)=>setEmail(e.target.value),\n                                                        placeholder: \"<EMAIL>\",\n                                                        className: \"pr-10\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"كلمة المرور\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        type: showPassword ? \"text\" : \"password\",\n                                                        value: password,\n                                                        onChange: (e)=>setPassword(e.target.value),\n                                                        placeholder: \"••••••••\",\n                                                        className: \"pr-10 pl-10\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setShowPassword(!showPassword),\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2\",\n                                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-red-600 bg-red-50 p-3 rounded-md\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"submit\",\n                                        className: \"w-full\",\n                                        disabled: isLoading,\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"جاري تسجيل الدخول...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"ml-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"تسجيل الدخول\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-xl font-bold\",\n                                    children: \"حسابات التجربة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"اختر أي حساب للتجربة - كل حساب له صلاحيات مختلفة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: demoAccounts.map((account, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 rounded-full \".concat(account.color)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: account.role\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                                    lineNumber: 155,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: account.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>fillDemoAccount(account.email, account.password),\n                                                    children: \"استخدام\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 p-4 bg-blue-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-blue-900 mb-2\",\n                                            children: \"معلومات الصلاحيات:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-blue-800 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"مدير النظام:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" صلاحيات كاملة لجميع الأقسام\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"مدير المبيعات:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" إدارة العملاء والمبيعات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"موظفة مبيعات:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" إضافة وتعديل العملاء والطلبات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"مسؤول المخزون:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" إدارة المنتجات والمخزون\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"مراجعة:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" عرض البيانات فقط\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"محاسب:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" التقارير المالية والمبيعات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-4 bg-green-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-green-900 mb-2\",\n                                            children: \"روابط سريعة:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-2 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/login\",\n                                                    className: \"text-green-700 hover:underline\",\n                                                    children: \"\\uD83D\\uDD10 صفحة تسجيل الدخول\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/dashboard\",\n                                                    className: \"text-green-700 hover:underline\",\n                                                    children: \"\\uD83D\\uDCCA لوحة التحكم\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/customers\",\n                                                    className: \"text-green-700 hover:underline\",\n                                                    children: \"\\uD83D\\uDC65 إدارة العملاء\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/inventory\",\n                                                    className: \"text-green-700 hover:underline\",\n                                                    children: \"\\uD83D\\uDCE6 إدارة المخزون\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/login-form.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginForm, \"QjaKSJEVQp1wB0abSM6uGwqVdw4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = LoginForm;\nvar _c;\n$RefreshReg$(_c, \"LoginForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/login-form.tsx\n"));

/***/ })

});