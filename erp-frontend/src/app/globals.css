@import "tailwindcss";

/* دعم RTL للعربية */
[dir="rtl"] {
  direction: rtl;
}

[dir="ltr"] {
  direction: ltr;
}

/* متغيرات CSS للألوان */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;
}

@theme inline {
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --font-sans: 'Cairo', 'Inter', var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
  }
}

* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: 'Cairo', 'Inter', sans-serif;
}

/* خطوط عربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

/* تحسينات للجداول الكبيرة */
.table-container {
  overflow-x: auto;
  max-width: 100%;
}

.table-responsive {
  min-width: 800px;
}

/* تحسينات للموبايل */
@media (max-width: 768px) {
  .table-responsive {
    min-width: 100%;
  }

  .mobile-card {
    display: block;
  }

  .desktop-table {
    display: none;
  }
}

@media (min-width: 769px) {
  .mobile-card {
    display: none;
  }

  .desktop-table {
    display: table;
  }
}

/* تحسينات للطباعة */
@media print {
  .no-print {
    display: none !important;
  }
}

/* تحسينات نظام المصادقة */
.auth-card {
  @apply bg-card border shadow-lg;
}

.auth-demo-account {
  @apply transition-all duration-200 hover:shadow-md;
}

.auth-role-badge {
  @apply w-4 h-4 rounded-full shadow-sm;
}

/* تحسين الألوان للأدوار */
.role-admin {
  @apply bg-red-500 text-red-50;
}

.role-manager {
  @apply bg-blue-500 text-blue-50;
}

.role-employee {
  @apply bg-green-500 text-green-50;
}

.role-viewer {
  @apply bg-purple-500 text-purple-50;
}

/* تحسينات إضافية للتصميم */
.gradient-bg {
  background: linear-gradient(135deg, hsl(var(--primary) / 0.1) 0%, hsl(var(--secondary) / 0.1) 100%);
}

.glass-effect {
  backdrop-filter: blur(10px);
  background: hsl(var(--background) / 0.8);
}

/* تحسين الانتقالات */
.smooth-transition {
  transition: all 0.2s ease-in-out;
}

/* تحسين الظلال */
.custom-shadow {
  box-shadow: 0 4px 6px -1px hsl(var(--foreground) / 0.1), 0 2px 4px -1px hsl(var(--foreground) / 0.06);
}

.custom-shadow-lg {
  box-shadow: 0 10px 15px -3px hsl(var(--foreground) / 0.1), 0 4px 6px -2px hsl(var(--foreground) / 0.05);
}
