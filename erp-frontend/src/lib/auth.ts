import bcrypt from 'bcryptjs'

export interface User {
  id: string
  email: string
  password: string
  name: string
  role: 'admin' | 'manager' | 'employee' | 'viewer'
  permissions: Permission[]
  department?: string
  avatar?: string
  isActive: boolean
  lastLogin?: Date
  createdAt: Date
}

export interface Permission {
  module: string
  actions: ('read' | 'write' | 'delete' | 'admin')[]
}

// قاعدة بيانات مؤقتة للمستخدمين
const users: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    password: bcrypt.hashSync('admin123', 10),
    name: 'مدير النظام',
    role: 'admin',
    permissions: [
      { module: 'dashboard', actions: ['read', 'write', 'delete', 'admin'] },
      { module: 'customers', actions: ['read', 'write', 'delete', 'admin'] },
      { module: 'inventory', actions: ['read', 'write', 'delete', 'admin'] },
      { module: 'sales', actions: ['read', 'write', 'delete', 'admin'] },
      { module: 'reports', actions: ['read', 'write', 'delete', 'admin'] },
      { module: 'settings', actions: ['read', 'write', 'delete', 'admin'] },
      { module: 'users', actions: ['read', 'write', 'delete', 'admin'] },
    ],
    department: 'إدارة',
    isActive: true,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: bcrypt.hashSync('manager123', 10),
    name: 'أحمد محمد - مدير المبيعات',
    role: 'manager',
    permissions: [
      { module: 'dashboard', actions: ['read'] },
      { module: 'customers', actions: ['read', 'write'] },
      { module: 'inventory', actions: ['read'] },
      { module: 'sales', actions: ['read', 'write', 'delete'] },
      { module: 'reports', actions: ['read'] },
    ],
    department: 'المبيعات',
    isActive: true,
    createdAt: new Date('2024-01-15'),
  },
  {
    id: '3',
    email: '<EMAIL>',
    password: bcrypt.hashSync('employee123', 10),
    name: 'فاطمة علي - موظفة مبيعات',
    role: 'employee',
    permissions: [
      { module: 'dashboard', actions: ['read'] },
      { module: 'customers', actions: ['read', 'write'] },
      { module: 'sales', actions: ['read', 'write'] },
    ],
    department: 'المبيعات',
    isActive: true,
    createdAt: new Date('2024-02-01'),
  },
  {
    id: '4',
    email: '<EMAIL>',
    password: bcrypt.hashSync('warehouse123', 10),
    name: 'محمد حسن - مسؤول المخزون',
    role: 'employee',
    permissions: [
      { module: 'dashboard', actions: ['read'] },
      { module: 'inventory', actions: ['read', 'write'] },
      { module: 'reports', actions: ['read'] },
    ],
    department: 'المخزون',
    isActive: true,
    createdAt: new Date('2024-02-10'),
  },
  {
    id: '5',
    email: '<EMAIL>',
    password: bcrypt.hashSync('viewer123', 10),
    name: 'سارة أحمد - مراجعة',
    role: 'viewer',
    permissions: [
      { module: 'dashboard', actions: ['read'] },
      { module: 'customers', actions: ['read'] },
      { module: 'inventory', actions: ['read'] },
      { module: 'sales', actions: ['read'] },
      { module: 'reports', actions: ['read'] },
    ],
    department: 'المراجعة',
    isActive: true,
    createdAt: new Date('2024-02-15'),
  },
  {
    id: '6',
    email: '<EMAIL>',
    password: bcrypt.hashSync('accountant123', 10),
    name: 'عمر خالد - محاسب',
    role: 'manager',
    permissions: [
      { module: 'dashboard', actions: ['read'] },
      { module: 'sales', actions: ['read'] },
      { module: 'reports', actions: ['read', 'write'] },
      { module: 'customers', actions: ['read'] },
    ],
    department: 'المحاسبة',
    isActive: true,
    createdAt: new Date('2024-02-20'),
  },
]

export async function authenticateUser(email: string, password: string): Promise<User | null> {
  const user = users.find(u => u.email === email && u.isActive)
  
  if (!user) {
    return null
  }

  const isValidPassword = await bcrypt.compare(password, user.password)
  
  if (!isValidPassword) {
    return null
  }

  // تحديث آخر تسجيل دخول
  user.lastLogin = new Date()
  
  return user
}

export function getUserById(id: string): User | null {
  return users.find(u => u.id === id && u.isActive) || null
}

export function getAllUsers(): User[] {
  return users.filter(u => u.isActive)
}

export function hasPermission(user: User, module: string, action: 'read' | 'write' | 'delete' | 'admin'): boolean {
  if (user.role === 'admin') {
    return true
  }

  const permission = user.permissions.find(p => p.module === module)
  return permission ? permission.actions.includes(action) : false
}

export function canAccessModule(user: User, module: string): boolean {
  return hasPermission(user, module, 'read')
}

// دالة للحصول على المستخدمين حسب الدور
export function getUsersByRole(role: User['role']): User[] {
  return users.filter(u => u.role === role && u.isActive)
}

// دالة للحصول على المستخدمين حسب القسم
export function getUsersByDepartment(department: string): User[] {
  return users.filter(u => u.department === department && u.isActive)
}
