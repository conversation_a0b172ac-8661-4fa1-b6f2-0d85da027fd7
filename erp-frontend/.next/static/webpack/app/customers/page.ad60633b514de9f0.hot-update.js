"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/customers/page",{

/***/ "(app-pages-browser)/./src/components/auth/protected-route.tsx":
/*!*************************************************!*\
  !*** ./src/components/auth/protected-route.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PermissionGuard: () => (/* binding */ PermissionGuard),\n/* harmony export */   ProtectedRoute: () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ ProtectedRoute,PermissionGuard auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ProtectedRoute(param) {\n    let { children, module, action = 'read', fallback } = param;\n    _s();\n    const { user, isLoading, isAuthenticated } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated) {\n                router.push('/login');\n            }\n        }\n    }[\"ProtectedRoute.useEffect\"], [\n        isLoading,\n        isAuthenticated,\n        router\n    ]);\n    // Loading state\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"جاري التحقق من الصلاحيات...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this);\n    }\n    // Not authenticated\n    if (!isAuthenticated || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-background p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"w-full max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-red-100 rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-8 w-8 text-red-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                className: \"text-xl font-bold text-red-600\",\n                                children: \"غير مصرح بالدخول\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                children: \"يجب تسجيل الدخول للوصول إلى هذه الصفحة\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            onClick: ()=>router.push('/login'),\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"ml-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 15\n                                }, this),\n                                \"الذهاب لتسجيل الدخول\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this);\n    }\n    // Check module permissions\n    if (module && !(0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.hasPermission)(user, module, action)) {\n        if (fallback) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: fallback\n            }, void 0, false);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-orange-100 p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"w-full max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-orange-100 rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-8 w-8 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                className: \"text-xl font-bold text-orange-600\",\n                                children: \"ليس لديك صلاحية\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                children: \"ليس لديك صلاحية للوصول إلى هذا القسم\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-orange-900 mb-2\",\n                                        children: \"معلومات حسابك:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-orange-800 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"الاسم:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 22\n                                                    }, this),\n                                                    \" \",\n                                                    user.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"الدور:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 22\n                                                    }, this),\n                                                    \" \",\n                                                    user.role\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"القسم:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 22\n                                                    }, this),\n                                                    \" \",\n                                                    user.department\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-blue-900 mb-2\",\n                                        children: \"الصلاحيات المتاحة:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-blue-800 space-y-1\",\n                                        children: user.permissions.map((perm, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: [\n                                                            perm.module,\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" \",\n                                                    perm.actions.join(', ')\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: ()=>router.back(),\n                                        variant: \"outline\",\n                                        className: \"flex-1\",\n                                        children: \"العودة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: ()=>router.push('/dashboard'),\n                                        className: \"flex-1\",\n                                        children: \"لوحة التحكم\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/components/auth/protected-route.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(ProtectedRoute, \"kxF4ulbmLJTZQS84anwmFclQxXE=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = ProtectedRoute;\nfunction PermissionGuard(param) {\n    let { module, action, children, fallback } = param;\n    _s1();\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    if (!user || !(0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.hasPermission)(user, module, action)) {\n        if (fallback) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: fallback\n            }, void 0, false);\n        }\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s1(PermissionGuard, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__.useAuth\n    ];\n});\n_c1 = PermissionGuard;\nvar _c, _c1;\n$RefreshReg$(_c, \"ProtectedRoute\");\n$RefreshReg$(_c1, \"PermissionGuard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/protected-route.tsx\n"));

/***/ })

});