"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./src/components/ui/data-table.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Sample data for demonstration\nconst recentOrders = [\n    {\n        id: \"ORD-001\",\n        customer: \"أحمد محمد\",\n        amount: 1250.00,\n        status: \"مكتمل\",\n        date: \"2024-01-15\"\n    },\n    {\n        id: \"ORD-002\",\n        customer: \"فاطمة علي\",\n        amount: 850.00,\n        status: \"قيد المعالجة\",\n        date: \"2024-01-14\"\n    },\n    {\n        id: \"ORD-003\",\n        customer: \"محمد حسن\",\n        amount: 2100.00,\n        status: \"مكتمل\",\n        date: \"2024-01-13\"\n    },\n    {\n        id: \"ORD-004\",\n        customer: \"سارة أحمد\",\n        amount: 750.00,\n        status: \"ملغي\",\n        date: \"2024-01-12\"\n    },\n    {\n        id: \"ORD-005\",\n        customer: \"عمر خالد\",\n        amount: 1500.00,\n        status: \"مكتمل\",\n        date: \"2024-01-11\"\n    }\n];\nconst orderColumns = [\n    {\n        key: \"id\",\n        title: \"رقم الطلب\",\n        sortable: true\n    },\n    {\n        key: \"customer\",\n        title: \"العميل\",\n        sortable: true\n    },\n    {\n        key: \"amount\",\n        title: \"المبلغ\",\n        sortable: true,\n        render: (value)=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(value)\n    },\n    {\n        key: \"status\",\n        title: \"الحالة\",\n        render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(value === \"مكتمل\" ? \"bg-green-100 text-green-800\" : value === \"قيد المعالجة\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                children: value\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined)\n    },\n    {\n        key: \"date\",\n        title: \"التاريخ\",\n        sortable: true,\n        render: (value)=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatDate)(value)\n    }\n];\nconst stats = [\n    {\n        title: \"إجمالي المبيعات\",\n        value: \"125,430\",\n        unit: \"ج.م\",\n        change: \"+12.5%\",\n        trend: \"up\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        title: \"عدد العملاء\",\n        value: \"1,234\",\n        unit: \"عميل\",\n        change: \"+8.2%\",\n        trend: \"up\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        title: \"المنتجات\",\n        value: \"856\",\n        unit: \"منتج\",\n        change: \"-2.1%\",\n        trend: \"down\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        title: \"الطلبات\",\n        value: \"342\",\n        unit: \"طلب\",\n        change: \"+15.3%\",\n        trend: \"up\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    }\n];\nconst alerts = [\n    {\n        type: \"warning\",\n        message: \"مخزون منخفض: 5 منتجات تحتاج إعادة تموين\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        type: \"success\",\n        message: \"تم تحديث أسعار 15 منتج بنجاح\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        type: \"warning\",\n        message: \"3 فواتير متأخرة السداد\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    }\n];\nfunction DashboardPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_1__.MainLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"لوحة التحكم\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"مرحباً بك في نظام إدارة الموارد المؤسسية\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-4\",\n                    children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: stat.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: [\n                                                stat.value,\n                                                \" \",\n                                                stat.unit\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground flex items-center\",\n                                            children: [\n                                                stat.trend === \"up\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-3 w-3 text-green-500 ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-3 w-3 text-red-500 ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 21\n                                                }, this),\n                                                stat.change,\n                                                \" من الشهر الماضي\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"التنبيهات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: \"التنبيهات والإشعارات المهمة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: alerts.map((alert, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center p-3 rounded-lg \".concat(alert.type === \"warning\" ? \"bg-yellow-50 border border-yellow-200\" : \"bg-green-50 border border-green-200\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(alert.icon, {\n                                                className: \"h-5 w-5 ml-3 \".concat(alert.type === \"warning\" ? \"text-yellow-600\" : \"text-green-600\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: alert.message\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"الطلبات الأخيرة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: \"آخر الطلبات المسجلة في النظام\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_3__.DataTable, {\n                                data: recentOrders,\n                                columns: orderColumns,\n                                pageSize: 5,\n                                mobileCardRender: (order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: order.id\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(order.status === \"مكتمل\" ? \"bg-green-100 text-green-800\" : order.status === \"قيد المعالجة\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                        children: order.status\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"العميل: \",\n                                                    order.customer\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(order.amount)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatDate)(order.date)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, void 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n            lineNumber: 156,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/erp/erp-frontend/src/app/dashboard/page.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});