{"version": 3, "file": "index.js", "sources": ["../src/util.js", "../src/pretty.js", "../src/index.js"], "sourcesContent": ["// DOM properties that should NOT have \"px\" added when numeric\nexport const IS_NON_DIMENSIONAL = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i;\nexport const VOID_ELEMENTS = /^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/;\nexport const UNSAFE_NAME = /[\\s\\n\\\\/='\"\\0<>]/;\nexport const XLINK = /^xlink:?./;\n\nconst ENCODED_ENTITIES = /[\"&<]/;\n\nexport function encodeEntities(str) {\n\t// Ensure we're always parsing and returning a string:\n\tstr += '';\n\n\t// Skip all work for strings with no entities needing encoding:\n\tif (ENCODED_ENTITIES.test(str) === false) return str;\n\n\tlet last = 0,\n\t\ti = 0,\n\t\tout = '',\n\t\tch = '';\n\n\t// Seek forward in str until the next entity char:\n\tfor (; i < str.length; i++) {\n\t\tswitch (str.charCodeAt(i)) {\n\t\t\tcase 34:\n\t\t\t\tch = '&quot;';\n\t\t\t\tbreak;\n\t\t\tcase 38:\n\t\t\t\tch = '&amp;';\n\t\t\t\tbreak;\n\t\t\tcase 60:\n\t\t\t\tch = '&lt;';\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tcontinue;\n\t\t}\n\t\t// Append skipped/buffered characters and the encoded entity:\n\t\tif (i !== last) out += str.slice(last, i);\n\t\tout += ch;\n\t\t// Start the next seek/buffer after the entity's offset:\n\t\tlast = i + 1;\n\t}\n\tif (i !== last) out += str.slice(last, i);\n\treturn out;\n}\n\nexport let indent = (s, char) =>\n\tString(s).replace(/(\\n+)/g, '$1' + (char || '\\t'));\n\nexport let isLargeString = (s, length, ignoreLines) =>\n\tString(s).length > (length || 40) ||\n\t(!ignoreLines && String(s).indexOf('\\n') !== -1) ||\n\tString(s).indexOf('<') !== -1;\n\nconst JS_TO_CSS = {};\n\nconst CSS_REGEX = /([A-Z])/g;\n// Convert an Object style to a CSSText string\nexport function styleObjToCss(s) {\n\tlet str = '';\n\tfor (let prop in s) {\n\t\tlet val = s[prop];\n\t\tif (val != null && val !== '') {\n\t\t\tif (str) str += ' ';\n\t\t\t// str += jsToCss(prop);\n\t\t\tstr +=\n\t\t\t\tprop[0] == '-'\n\t\t\t\t\t? prop\n\t\t\t\t\t: JS_TO_CSS[prop] ||\n\t\t\t\t\t  (JS_TO_CSS[prop] = prop.replace(CSS_REGEX, '-$1').toLowerCase());\n\n\t\t\tif (typeof val === 'number' && IS_NON_DIMENSIONAL.test(prop) === false) {\n\t\t\t\tstr = str + ': ' + val + 'px;';\n\t\t\t} else {\n\t\t\t\tstr = str + ': ' + val + ';';\n\t\t\t}\n\t\t}\n\t}\n\treturn str || undefined;\n}\n\n/**\n * Get flattened children from the children prop\n * @param {Array} accumulator\n * @param {any} children A `props.children` opaque object.\n * @returns {Array} accumulator\n * @private\n */\nexport function getChildren(accumulator, children) {\n\tif (Array.isArray(children)) {\n\t\tchildren.reduce(getChildren, accumulator);\n\t} else if (children != null && children !== false) {\n\t\taccumulator.push(children);\n\t}\n\treturn accumulator;\n}\n\nfunction markAsDirty() {\n\tthis.__d = true;\n}\n\nexport function createComponent(vnode, context) {\n\treturn {\n\t\t__v: vnode,\n\t\tcontext,\n\t\tprops: vnode.props,\n\t\t// silently drop state updates\n\t\tsetState: markAsDirty,\n\t\tforceUpdate: markAsDirty,\n\t\t__d: true,\n\t\t// hooks\n\t\t__h: []\n\t};\n}\n\n// Necessary for createContext api. Setting this property will pass\n// the context value as `this.context` just for this component.\nexport function getContext(nodeName, context) {\n\tlet cxType = nodeName.contextType;\n\tlet provider = cxType && context[cxType.__c];\n\treturn cxType != null\n\t\t? provider\n\t\t\t? provider.props.value\n\t\t\t: cxType.__\n\t\t: context;\n}\n", "import {\n\tencodeEntities,\n\tindent,\n\tisLargeString,\n\tstyleObjTo<PERSON>s,\n\tgetChildren,\n\tcreateComponent,\n\tgetContext,\n\tUNSAFE_NAME,\n\tXLINK,\n\tVOID_ELEMENTS\n} from './util';\nimport { options, Fragment } from 'preact';\n\n// components without names, kept as a hash for later comparison to return consistent UnnamedComponentXX names.\nconst UNNAMED = [];\n\nexport function _renderToStringPretty(\n\tvnode,\n\tcontext,\n\topts,\n\tinner,\n\tisSvgMode,\n\tselectValue\n) {\n\tif (vnode == null || typeof vnode === 'boolean') {\n\t\treturn '';\n\t}\n\n\t// #text nodes\n\tif (typeof vnode !== 'object') {\n\t\tif (typeof vnode === 'function') return '';\n\t\treturn encodeEntities(vnode);\n\t}\n\n\tlet pretty = opts.pretty,\n\t\tindentChar = pretty && typeof pretty === 'string' ? pretty : '\\t';\n\n\tif (Array.isArray(vnode)) {\n\t\tlet rendered = '';\n\t\tfor (let i = 0; i < vnode.length; i++) {\n\t\t\tif (pretty && i > 0) rendered = rendered + '\\n';\n\t\t\trendered =\n\t\t\t\trendered +\n\t\t\t\t_renderToStringPretty(\n\t\t\t\t\tvnode[i],\n\t\t\t\t\tcontext,\n\t\t\t\t\topts,\n\t\t\t\t\tinner,\n\t\t\t\t\tisSvgMode,\n\t\t\t\t\tselectValue\n\t\t\t\t);\n\t\t}\n\t\treturn rendered;\n\t}\n\n\t// VNodes have {constructor:undefined} to prevent JSON injection:\n\tif (vnode.constructor !== undefined) return '';\n\n\tlet nodeName = vnode.type,\n\t\tprops = vnode.props,\n\t\tisComponent = false;\n\n\t// components\n\tif (typeof nodeName === 'function') {\n\t\tisComponent = true;\n\t\tif (opts.shallow && (inner || opts.renderRootComponent === false)) {\n\t\t\tnodeName = getComponentName(nodeName);\n\t\t} else if (nodeName === Fragment) {\n\t\t\tconst children = [];\n\t\t\tgetChildren(children, vnode.props.children);\n\t\t\treturn _renderToStringPretty(\n\t\t\t\tchildren,\n\t\t\t\tcontext,\n\t\t\t\topts,\n\t\t\t\topts.shallowHighOrder !== false,\n\t\t\t\tisSvgMode,\n\t\t\t\tselectValue\n\t\t\t);\n\t\t} else {\n\t\t\tlet rendered;\n\n\t\t\tlet c = (vnode.__c = createComponent(vnode, context));\n\n\t\t\t// options._diff\n\t\t\tif (options.__b) options.__b(vnode);\n\n\t\t\t// options._render\n\t\t\tlet renderHook = options.__r;\n\n\t\t\tif (\n\t\t\t\t!nodeName.prototype ||\n\t\t\t\ttypeof nodeName.prototype.render !== 'function'\n\t\t\t) {\n\t\t\t\tlet cctx = getContext(nodeName, context);\n\n\t\t\t\t// If a hook invokes setState() to invalidate the component during rendering,\n\t\t\t\t// re-render it up to 25 times to allow \"settling\" of memoized states.\n\t\t\t\t// Note:\n\t\t\t\t//   This will need to be updated for Preact 11 to use internal.flags rather than component._dirty:\n\t\t\t\t//   https://github.com/preactjs/preact/blob/d4ca6fdb19bc715e49fd144e69f7296b2f4daa40/src/diff/component.js#L35-L44\n\t\t\t\tlet count = 0;\n\t\t\t\twhile (c.__d && count++ < 25) {\n\t\t\t\t\tc.__d = false;\n\n\t\t\t\t\tif (renderHook) renderHook(vnode);\n\n\t\t\t\t\t// stateless functional components\n\t\t\t\t\trendered = nodeName.call(vnode.__c, props, cctx);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tlet cctx = getContext(nodeName, context);\n\n\t\t\t\t// c = new nodeName(props, context);\n\t\t\t\tc = vnode.__c = new nodeName(props, cctx);\n\t\t\t\tc.__v = vnode;\n\t\t\t\t// turn off stateful re-rendering:\n\t\t\t\tc._dirty = c.__d = true;\n\t\t\t\tc.props = props;\n\t\t\t\tif (c.state == null) c.state = {};\n\n\t\t\t\tif (c._nextState == null && c.__s == null) {\n\t\t\t\t\tc._nextState = c.__s = c.state;\n\t\t\t\t}\n\n\t\t\t\tc.context = cctx;\n\t\t\t\tif (nodeName.getDerivedStateFromProps)\n\t\t\t\t\tc.state = Object.assign(\n\t\t\t\t\t\t{},\n\t\t\t\t\t\tc.state,\n\t\t\t\t\t\tnodeName.getDerivedStateFromProps(c.props, c.state)\n\t\t\t\t\t);\n\t\t\t\telse if (c.componentWillMount) {\n\t\t\t\t\tc.componentWillMount();\n\n\t\t\t\t\t// If the user called setState in cWM we need to flush pending,\n\t\t\t\t\t// state updates. This is the same behaviour in React.\n\t\t\t\t\tc.state =\n\t\t\t\t\t\tc._nextState !== c.state\n\t\t\t\t\t\t\t? c._nextState\n\t\t\t\t\t\t\t: c.__s !== c.state\n\t\t\t\t\t\t\t? c.__s\n\t\t\t\t\t\t\t: c.state;\n\t\t\t\t}\n\n\t\t\t\tif (renderHook) renderHook(vnode);\n\n\t\t\t\trendered = c.render(c.props, c.state, c.context);\n\t\t\t}\n\n\t\t\tif (c.getChildContext) {\n\t\t\t\tcontext = Object.assign({}, context, c.getChildContext());\n\t\t\t}\n\n\t\t\tif (options.diffed) options.diffed(vnode);\n\t\t\treturn _renderToStringPretty(\n\t\t\t\trendered,\n\t\t\t\tcontext,\n\t\t\t\topts,\n\t\t\t\topts.shallowHighOrder !== false,\n\t\t\t\tisSvgMode,\n\t\t\t\tselectValue\n\t\t\t);\n\t\t}\n\t}\n\n\t// render JSX to HTML\n\tlet s = '<' + nodeName,\n\t\tpropChildren,\n\t\thtml;\n\n\tif (props) {\n\t\tlet attrs = Object.keys(props);\n\n\t\t// allow sorting lexicographically for more determinism (useful for tests, such as via preact-jsx-chai)\n\t\tif (opts && opts.sortAttributes === true) attrs.sort();\n\n\t\tfor (let i = 0; i < attrs.length; i++) {\n\t\t\tlet name = attrs[i],\n\t\t\t\tv = props[name];\n\t\t\tif (name === 'children') {\n\t\t\t\tpropChildren = v;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (UNSAFE_NAME.test(name)) continue;\n\n\t\t\tif (\n\t\t\t\t!(opts && opts.allAttributes) &&\n\t\t\t\t(name === 'key' ||\n\t\t\t\t\tname === 'ref' ||\n\t\t\t\t\tname === '__self' ||\n\t\t\t\t\tname === '__source')\n\t\t\t)\n\t\t\t\tcontinue;\n\n\t\t\tif (name === 'defaultValue') {\n\t\t\t\tname = 'value';\n\t\t\t} else if (name === 'defaultChecked') {\n\t\t\t\tname = 'checked';\n\t\t\t} else if (name === 'defaultSelected') {\n\t\t\t\tname = 'selected';\n\t\t\t} else if (name === 'className') {\n\t\t\t\tif (typeof props.class !== 'undefined') continue;\n\t\t\t\tname = 'class';\n\t\t\t} else if (isSvgMode && XLINK.test(name)) {\n\t\t\t\tname = name.toLowerCase().replace(/^xlink:?/, 'xlink:');\n\t\t\t}\n\n\t\t\tif (name === 'htmlFor') {\n\t\t\t\tif (props.for) continue;\n\t\t\t\tname = 'for';\n\t\t\t}\n\n\t\t\tif (name === 'style' && v && typeof v === 'object') {\n\t\t\t\tv = styleObjToCss(v);\n\t\t\t}\n\n\t\t\t// always use string values instead of booleans for aria attributes\n\t\t\t// also see https://github.com/preactjs/preact/pull/2347/files\n\t\t\tif (name[0] === 'a' && name['1'] === 'r' && typeof v === 'boolean') {\n\t\t\t\tv = String(v);\n\t\t\t}\n\n\t\t\tlet hooked =\n\t\t\t\topts.attributeHook &&\n\t\t\t\topts.attributeHook(name, v, context, opts, isComponent);\n\t\t\tif (hooked || hooked === '') {\n\t\t\t\ts = s + hooked;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (name === 'dangerouslySetInnerHTML') {\n\t\t\t\thtml = v && v.__html;\n\t\t\t} else if (nodeName === 'textarea' && name === 'value') {\n\t\t\t\t// <textarea value=\"a&b\"> --> <textarea>a&amp;b</textarea>\n\t\t\t\tpropChildren = v;\n\t\t\t} else if ((v || v === 0 || v === '') && typeof v !== 'function') {\n\t\t\t\tif (v === true || v === '') {\n\t\t\t\t\tv = name;\n\t\t\t\t\t// in non-xml mode, allow boolean attributes\n\t\t\t\t\tif (!opts || !opts.xml) {\n\t\t\t\t\t\ts = s + ' ' + name;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (name === 'value') {\n\t\t\t\t\tif (nodeName === 'select') {\n\t\t\t\t\t\tselectValue = v;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else if (\n\t\t\t\t\t\t// If we're looking at an <option> and it's the currently selected one\n\t\t\t\t\t\tnodeName === 'option' &&\n\t\t\t\t\t\tselectValue == v &&\n\t\t\t\t\t\t// and the <option> doesn't already have a selected attribute on it\n\t\t\t\t\t\ttypeof props.selected === 'undefined'\n\t\t\t\t\t) {\n\t\t\t\t\t\ts = s + ` selected`;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\ts = s + ` ${name}=\"${encodeEntities(v)}\"`;\n\t\t\t}\n\t\t}\n\t}\n\n\t// account for >1 multiline attribute\n\tif (pretty) {\n\t\tlet sub = s.replace(/\\n\\s*/, ' ');\n\t\tif (sub !== s && !~sub.indexOf('\\n')) s = sub;\n\t\telse if (pretty && ~s.indexOf('\\n')) s = s + '\\n';\n\t}\n\n\ts = s + '>';\n\n\tif (UNSAFE_NAME.test(nodeName))\n\t\tthrow new Error(`${nodeName} is not a valid HTML tag name in ${s}`);\n\n\tlet isVoid =\n\t\tVOID_ELEMENTS.test(nodeName) ||\n\t\t(opts.voidElements && opts.voidElements.test(nodeName));\n\tlet pieces = [];\n\n\tlet children;\n\tif (html) {\n\t\t// if multiline, indent.\n\t\tif (pretty && isLargeString(html)) {\n\t\t\thtml = '\\n' + indentChar + indent(html, indentChar);\n\t\t}\n\t\ts = s + html;\n\t} else if (\n\t\tpropChildren != null &&\n\t\tgetChildren((children = []), propChildren).length\n\t) {\n\t\tlet hasLarge = pretty && ~s.indexOf('\\n');\n\t\tlet lastWasText = false;\n\n\t\tfor (let i = 0; i < children.length; i++) {\n\t\t\tlet child = children[i];\n\n\t\t\tif (child != null && child !== false) {\n\t\t\t\tlet childSvgMode =\n\t\t\t\t\t\tnodeName === 'svg'\n\t\t\t\t\t\t\t? true\n\t\t\t\t\t\t\t: nodeName === 'foreignObject'\n\t\t\t\t\t\t\t? false\n\t\t\t\t\t\t\t: isSvgMode,\n\t\t\t\t\tret = _renderToStringPretty(\n\t\t\t\t\t\tchild,\n\t\t\t\t\t\tcontext,\n\t\t\t\t\t\topts,\n\t\t\t\t\t\ttrue,\n\t\t\t\t\t\tchildSvgMode,\n\t\t\t\t\t\tselectValue\n\t\t\t\t\t);\n\n\t\t\t\tif (pretty && !hasLarge && isLargeString(ret)) hasLarge = true;\n\n\t\t\t\t// Skip if we received an empty string\n\t\t\t\tif (ret) {\n\t\t\t\t\tif (pretty) {\n\t\t\t\t\t\tlet isText = ret.length > 0 && ret[0] != '<';\n\n\t\t\t\t\t\t// We merge adjacent text nodes, otherwise each piece would be printed\n\t\t\t\t\t\t// on a new line.\n\t\t\t\t\t\tif (lastWasText && isText) {\n\t\t\t\t\t\t\tpieces[pieces.length - 1] += ret;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tpieces.push(ret);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tlastWasText = isText;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tpieces.push(ret);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (pretty && hasLarge) {\n\t\t\tfor (let i = pieces.length; i--; ) {\n\t\t\t\tpieces[i] = '\\n' + indentChar + indent(pieces[i], indentChar);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (pieces.length || html) {\n\t\ts = s + pieces.join('');\n\t} else if (opts && opts.xml) {\n\t\treturn s.substring(0, s.length - 1) + ' />';\n\t}\n\n\tif (isVoid && !children && !html) {\n\t\ts = s.replace(/>$/, ' />');\n\t} else {\n\t\tif (pretty && ~s.indexOf('\\n')) s = s + '\\n';\n\t\ts = s + `</${nodeName}>`;\n\t}\n\n\treturn s;\n}\n\nfunction getComponentName(component) {\n\treturn (\n\t\tcomponent.displayName ||\n\t\t(component !== Function && component.name) ||\n\t\tgetFallbackComponentName(component)\n\t);\n}\n\nfunction getFallbackComponentName(component) {\n\tlet str = Function.prototype.toString.call(component),\n\t\tname = (str.match(/^\\s*function\\s+([^( ]+)/) || '')[1];\n\tif (!name) {\n\t\t// search for an existing indexed name for the given component:\n\t\tlet index = -1;\n\t\tfor (let i = UNNAMED.length; i--; ) {\n\t\t\tif (UNNAMED[i] === component) {\n\t\t\t\tindex = i;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\t// not found, create a new indexed name:\n\t\tif (index < 0) {\n\t\t\tindex = UNNAMED.push(component) - 1;\n\t\t}\n\t\tname = `UnnamedComponent${index}`;\n\t}\n\treturn name;\n}\n", "import {\n\tencodeEntities,\n\tstyleObjTo<PERSON>s,\n\tgetContext,\n\tcreate<PERSON>omponent,\n\tUNSAFE_NAME,\n\tXLINK,\n\tVOID_ELEMENTS\n} from './util';\nimport { options, h, Fragment } from 'preact';\nimport { _renderToStringPretty } from './pretty';\nimport {\n\tCOMMIT,\n\tCOMPONENT,\n\tDIFF,\n\tDIFFED,\n\tDIRTY,\n\tNEXT_STATE,\n\tPARENT,\n\tRENDER,\n\tSKIP_EFFECTS,\n\tVNODE,\n\tCHILDREN\n} from './constants';\n\n/** @typedef {import('preact').VNode} VNode */\n\nconst SHALLOW = { shallow: true };\n\n/** Render Preact JSX + Components to an HTML string.\n *\t@name render\n *\t@function\n *\t@param {VNode} vnode\tJSX VNode to render.\n *\t@param {Object} [context={}]\tOptionally pass an initial context object through the render path.\n *\t@param {Object} [options={}]\tRendering options\n *\t@param {Boolean} [options.shallow=false]\tIf `true`, renders nested Components as HTML elements (`<Foo a=\"b\" />`).\n *\t@param {Boolean} [options.xml=false]\t\tIf `true`, uses self-closing tags for elements without children.\n *\t@param {Boolean} [options.pretty=false]\t\tIf `true`, adds whitespace for readability\n *\t@param {RegExp|undefined} [options.voidElements]       RegeEx that matches elements that are considered void (self-closing)\n */\nrenderToString.render = renderToString;\n\n/** Only render elements, leaving Components inline as `<ComponentName ... />`.\n *\tThis method is just a convenience alias for `render(vnode, context, { shallow:true })`\n *\t@name shallow\n *\t@function\n *\t@param {VNode} vnode\tJSX VNode to render.\n *\t@param {Object} [context={}]\tOptionally pass an initial context object through the render path.\n */\nlet shallowRender = (vnode, context) => renderToString(vnode, context, SHALLOW);\n\nconst EMPTY_ARR = [];\nfunction renderToString(vnode, context, opts) {\n\tcontext = context || {};\n\n\t// Performance optimization: `renderToString` is synchronous and we\n\t// therefore don't execute any effects. To do that we pass an empty\n\t// array to `options._commit` (`__c`). But we can go one step further\n\t// and avoid a lot of dirty checks and allocations by setting\n\t// `options._skipEffects` (`__s`) too.\n\tconst previousSkipEffects = options[SKIP_EFFECTS];\n\toptions[SKIP_EFFECTS] = true;\n\n\tconst parent = h(Fragment, null);\n\tparent[CHILDREN] = [vnode];\n\n\tlet res;\n\tif (\n\t\topts &&\n\t\t(opts.pretty ||\n\t\t\topts.voidElements ||\n\t\t\topts.sortAttributes ||\n\t\t\topts.shallow ||\n\t\t\topts.allAttributes ||\n\t\t\topts.xml ||\n\t\t\topts.attributeHook)\n\t) {\n\t\tres = _renderToStringPretty(vnode, context, opts);\n\t} else {\n\t\tres = _renderToString(vnode, context, false, undefined, parent);\n\t}\n\n\t// options._commit, we don't schedule any effects in this library right now,\n\t// so we can pass an empty queue to this hook.\n\tif (options[COMMIT]) options[COMMIT](vnode, EMPTY_ARR);\n\toptions[SKIP_EFFECTS] = previousSkipEffects;\n\tEMPTY_ARR.length = 0;\n\n\treturn res;\n}\n\n/**\n * @param {VNode} vnode\n * @param {Record<string, unknown>} context\n * @returns {string}\n */\nfunction renderFunctionComponent(vnode, context) {\n\t// eslint-disable-next-line lines-around-comment\n\t/** @type {string} */\n\tlet rendered,\n\t\tc = createComponent(vnode, context),\n\t\tcctx = getContext(vnode.type, context);\n\n\tvnode[COMPONENT] = c;\n\n\t// If a hook invokes setState() to invalidate the component during rendering,\n\t// re-render it up to 25 times to allow \"settling\" of memoized states.\n\t// Note:\n\t//   This will need to be updated for Preact 11 to use internal.flags rather than component._dirty:\n\t//   https://github.com/preactjs/preact/blob/d4ca6fdb19bc715e49fd144e69f7296b2f4daa40/src/diff/component.js#L35-L44\n\tlet renderHook = options[RENDER];\n\tlet count = 0;\n\twhile (c[DIRTY] && count++ < 25) {\n\t\tc[DIRTY] = false;\n\n\t\tif (renderHook) renderHook(vnode);\n\n\t\t// stateless functional components\n\t\trendered = vnode.type.call(c, vnode.props, cctx);\n\t}\n\n\treturn rendered;\n}\n\n/**\n * @param {VNode} vnode\n * @param {Record<string, unknown>} context\n * @returns {VNode}\n */\nfunction renderClassComponent(vnode, context) {\n\tlet nodeName = vnode.type,\n\t\tcctx = getContext(nodeName, context);\n\n\t/** @type {import(\"preact\").Component} */\n\tlet c = new nodeName(vnode.props, cctx);\n\tvnode[COMPONENT] = c;\n\tc[VNODE] = vnode;\n\t// turn off stateful re-rendering:\n\tc[DIRTY] = true;\n\tc.props = vnode.props;\n\tif (c.state == null) c.state = {};\n\n\tif (c[NEXT_STATE] == null) {\n\t\tc[NEXT_STATE] = c.state;\n\t}\n\n\tc.context = cctx;\n\tif (nodeName.getDerivedStateFromProps) {\n\t\tc.state = assign(\n\t\t\t{},\n\t\t\tc.state,\n\t\t\tnodeName.getDerivedStateFromProps(c.props, c.state)\n\t\t);\n\t} else if (c.componentWillMount) {\n\t\tc.componentWillMount();\n\n\t\t// If the user called setState in cWM we need to flush pending,\n\t\t// state updates. This is the same behaviour in React.\n\t\tc.state = c[NEXT_STATE] !== c.state ? c[NEXT_STATE] : c.state;\n\t}\n\n\tlet renderHook = options[RENDER];\n\tif (renderHook) renderHook(vnode);\n\n\treturn c.render(c.props, c.state, c.context);\n}\n\n/**\n * @param {any} vnode\n * @returns {VNode}\n */\nfunction normalizeVNode(vnode) {\n\tif (vnode == null || typeof vnode == 'boolean') {\n\t\treturn null;\n\t} else if (\n\t\ttypeof vnode == 'string' ||\n\t\ttypeof vnode == 'number' ||\n\t\ttypeof vnode == 'bigint'\n\t) {\n\t\treturn h(null, null, vnode);\n\t}\n\treturn vnode;\n}\n\n/**\n * @param {string} name\n * @param {boolean} isSvgMode\n * @returns {string}\n */\nfunction normalizePropName(name, isSvgMode) {\n\tif (name === 'className') {\n\t\treturn 'class';\n\t} else if (name === 'htmlFor') {\n\t\treturn 'for';\n\t} else if (name === 'defaultValue') {\n\t\treturn 'value';\n\t} else if (name === 'defaultChecked') {\n\t\treturn 'checked';\n\t} else if (name === 'defaultSelected') {\n\t\treturn 'selected';\n\t} else if (isSvgMode && XLINK.test(name)) {\n\t\treturn name.toLowerCase().replace(/^xlink:?/, 'xlink:');\n\t}\n\n\treturn name;\n}\n\n/**\n * @param {string} name\n * @param {string | Record<string, unknown>} v\n * @returns {string}\n */\nfunction normalizePropValue(name, v) {\n\tif (name === 'style' && v != null && typeof v === 'object') {\n\t\treturn styleObjToCss(v);\n\t} else if (name[0] === 'a' && name[1] === 'r' && typeof v === 'boolean') {\n\t\t// always use string values instead of booleans for aria attributes\n\t\t// also see https://github.com/preactjs/preact/pull/2347/files\n\t\treturn String(v);\n\t}\n\n\treturn v;\n}\n\nconst isArray = Array.isArray;\nconst assign = Object.assign;\n\n/**\n * The default export is an alias of `render()`.\n * @param {any} vnode\n * @param {Record<string, unknown>} context\n * @param {boolean} isSvgMode\n * @param {any} selectValue\n * @param {VNode | null} parent\n * @returns {string}\n */\nfunction _renderToString(vnode, context, isSvgMode, selectValue, parent) {\n\t// Ignore non-rendered VNodes/values\n\tif (vnode == null || vnode === true || vnode === false || vnode === '') {\n\t\treturn '';\n\t}\n\n\t// Text VNodes: escape as HTML\n\tif (typeof vnode !== 'object') {\n\t\tif (typeof vnode === 'function') return '';\n\t\treturn encodeEntities(vnode);\n\t}\n\n\t// Recurse into children / Arrays\n\tif (isArray(vnode)) {\n\t\tlet rendered = '';\n\t\tparent[CHILDREN] = vnode;\n\t\tfor (let i = 0; i < vnode.length; i++) {\n\t\t\trendered =\n\t\t\t\trendered +\n\t\t\t\t_renderToString(vnode[i], context, isSvgMode, selectValue, parent);\n\n\t\t\tvnode[i] = normalizeVNode(vnode[i]);\n\t\t}\n\t\treturn rendered;\n\t}\n\n\t// VNodes have {constructor:undefined} to prevent JSON injection:\n\tif (vnode.constructor !== undefined) return '';\n\n\tvnode[PARENT] = parent;\n\tif (options[DIFF]) options[DIFF](vnode);\n\n\tlet type = vnode.type,\n\t\tprops = vnode.props;\n\n\t// Invoke rendering on Components\n\tconst isComponent = typeof type === 'function';\n\tif (isComponent) {\n\t\tlet rendered;\n\t\tif (type === Fragment) {\n\t\t\trendered = props.children;\n\t\t} else {\n\t\t\tif (type.prototype && typeof type.prototype.render === 'function') {\n\t\t\t\trendered = renderClassComponent(vnode, context);\n\t\t\t} else {\n\t\t\t\trendered = renderFunctionComponent(vnode, context);\n\t\t\t}\n\n\t\t\tlet component = vnode[COMPONENT];\n\t\t\tif (component.getChildContext) {\n\t\t\t\tcontext = assign({}, context, component.getChildContext());\n\t\t\t}\n\t\t}\n\n\t\t// When a component returns a Fragment node we flatten it in core, so we\n\t\t// need to mirror that logic here too\n\t\tlet isTopLevelFragment =\n\t\t\trendered != null && rendered.type === Fragment && rendered.key == null;\n\t\trendered = isTopLevelFragment ? rendered.props.children : rendered;\n\n\t\t// Recurse into children before invoking the after-diff hook\n\t\tconst str = _renderToString(\n\t\t\trendered,\n\t\t\tcontext,\n\t\t\tisSvgMode,\n\t\t\tselectValue,\n\t\t\tvnode\n\t\t);\n\n\t\tif (options[DIFFED]) options[DIFFED](vnode);\n\t\tvnode[PARENT] = undefined;\n\n\t\tif (options.unmount) options.unmount(vnode);\n\n\t\treturn str;\n\t}\n\n\t// Serialize Element VNodes to HTML\n\tlet s = '<',\n\t\tchildren,\n\t\thtml;\n\n\ts = s + type;\n\n\tif (props) {\n\t\tchildren = props.children;\n\t\tfor (let name in props) {\n\t\t\tlet v = props[name];\n\n\t\t\tif (\n\t\t\t\tname === 'key' ||\n\t\t\t\tname === 'ref' ||\n\t\t\t\tname === '__self' ||\n\t\t\t\tname === '__source' ||\n\t\t\t\tname === 'children' ||\n\t\t\t\t(name === 'className' && 'class' in props) ||\n\t\t\t\t(name === 'htmlFor' && 'for' in props)\n\t\t\t) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (UNSAFE_NAME.test(name)) continue;\n\n\t\t\tname = normalizePropName(name, isSvgMode);\n\t\t\tv = normalizePropValue(name, v);\n\n\t\t\tif (name === 'dangerouslySetInnerHTML') {\n\t\t\t\thtml = v && v.__html;\n\t\t\t} else if (type === 'textarea' && name === 'value') {\n\t\t\t\t// <textarea value=\"a&b\"> --> <textarea>a&amp;b</textarea>\n\t\t\t\tchildren = v;\n\t\t\t} else if ((v || v === 0 || v === '') && typeof v !== 'function') {\n\t\t\t\tif (v === true || v === '') {\n\t\t\t\t\tv = name;\n\t\t\t\t\ts = s + ' ' + name;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (name === 'value') {\n\t\t\t\t\tif (type === 'select') {\n\t\t\t\t\t\tselectValue = v;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else if (\n\t\t\t\t\t\t// If we're looking at an <option> and it's the currently selected one\n\t\t\t\t\t\ttype === 'option' &&\n\t\t\t\t\t\tselectValue == v &&\n\t\t\t\t\t\t// and the <option> doesn't already have a selected attribute on it\n\t\t\t\t\t\t!('selected' in props)\n\t\t\t\t\t) {\n\t\t\t\t\t\ts = s + ' selected';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\ts = s + ' ' + name + '=\"' + encodeEntities(v) + '\"';\n\t\t\t}\n\t\t}\n\t}\n\n\tlet startElement = s;\n\ts = s + '>';\n\n\tif (UNSAFE_NAME.test(type)) {\n\t\tthrow new Error(`${type} is not a valid HTML tag name in ${s}`);\n\t}\n\n\tlet pieces = '';\n\tlet hasChildren = false;\n\n\tif (html) {\n\t\tpieces = pieces + html;\n\t\thasChildren = true;\n\t} else if (typeof children === 'string') {\n\t\tpieces = pieces + encodeEntities(children);\n\t\thasChildren = true;\n\t} else if (isArray(children)) {\n\t\tvnode[CHILDREN] = children;\n\t\tfor (let i = 0; i < children.length; i++) {\n\t\t\tlet child = children[i];\n\t\t\tchildren[i] = normalizeVNode(child);\n\n\t\t\tif (child != null && child !== false) {\n\t\t\t\tlet childSvgMode =\n\t\t\t\t\ttype === 'svg' || (type !== 'foreignObject' && isSvgMode);\n\t\t\t\tlet ret = _renderToString(\n\t\t\t\t\tchild,\n\t\t\t\t\tcontext,\n\t\t\t\t\tchildSvgMode,\n\t\t\t\t\tselectValue,\n\t\t\t\t\tvnode\n\t\t\t\t);\n\n\t\t\t\t// Skip if we received an empty string\n\t\t\t\tif (ret) {\n\t\t\t\t\tpieces = pieces + ret;\n\t\t\t\t\thasChildren = true;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t} else if (children != null && children !== false && children !== true) {\n\t\tvnode[CHILDREN] = [normalizeVNode(children)];\n\t\tlet childSvgMode =\n\t\t\ttype === 'svg' || (type !== 'foreignObject' && isSvgMode);\n\t\tlet ret = _renderToString(\n\t\t\tchildren,\n\t\t\tcontext,\n\t\t\tchildSvgMode,\n\t\t\tselectValue,\n\t\t\tvnode\n\t\t);\n\n\t\t// Skip if we received an empty string\n\t\tif (ret) {\n\t\t\tpieces = pieces + ret;\n\t\t\thasChildren = true;\n\t\t}\n\t}\n\n\tif (options[DIFFED]) options[DIFFED](vnode);\n\tvnode[PARENT] = undefined;\n\tif (options.unmount) options.unmount(vnode);\n\n\tif (hasChildren) {\n\t\ts = s + pieces;\n\t} else if (VOID_ELEMENTS.test(type)) {\n\t\treturn startElement + ' />';\n\t}\n\n\treturn s + '</' + type + '>';\n}\n\n/** The default export is an alias of `render()`. */\n\nrenderToString.shallowRender = shallowRender;\n\nexport default renderToString;\n\nexport {\n\trenderToString as render,\n\trenderToString as renderToStaticMarkup,\n\trenderToString,\n\tshallowRender\n};\n"], "names": ["IS_NON_DIMENSIONAL", "VOID_ELEMENTS", "UNSAFE_NAME", "XLINK", "ENCODED_ENTITIES", "encodeEntities", "str", "test", "last", "i", "out", "ch", "length", "charCodeAt", "slice", "indent", "s", "char", "String", "replace", "isLargeString", "ignoreLines", "indexOf", "JS_TO_CSS", "CSS_REGEX", "styleObjToCss", "prop", "val", "toLowerCase", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "accumulator", "children", "Array", "isArray", "reduce", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "this", "__d", "createComponent", "vnode", "context", "__v", "props", "setState", "forceUpdate", "__h", "getContext", "nodeName", "cxType", "contextType", "provider", "__c", "value", "__", "UNNAMED", "_renderToStringPretty", "opts", "inner", "isSvgMode", "selectValue", "pretty", "indentChar", "rendered", "constructor", "component", "type", "isComponent", "shallow", "renderRootComponent", "Fragment", "shallowHighOrder", "c", "options", "__b", "renderHook", "__r", "prototype", "render", "cctx", "_dirty", "state", "_nextState", "__s", "getDerivedStateFromProps", "Object", "assign", "componentWillMount", "count", "call", "getChildContext", "diffed", "displayName", "Function", "name", "toString", "match", "index", "getFallbackComponentName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "html", "attrs", "keys", "sortAttributes", "sort", "v", "allAttributes", "hooked", "attributeHook", "__html", "xml", "selected", "sub", "Error", "isVoid", "voidElements", "pieces", "<PERSON><PERSON><PERSON><PERSON>", "lastWasText", "child", "ret", "isText", "join", "substring", "SHALLOW", "renderToString", "shallowRender", "EMPTY_ARR", "previousSkipEffects", "res", "parent", "h", "_renderToString", "normalizeVNode", "normalizePropName", "normalizePropValue", "renderClassComponent", "renderFunctionComponent", "key", "unmount", "startElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "wRACaA,EAAqB,kEACrBC,EAAgB,2EAChBC,EAAc,mBACdC,EAAQ,YAEfC,EAAmB,iBAETC,EAAeC,GAK9B,IAAmC,IAA/BF,EAAiBG,KAHrBD,GAAO,IAGmC,OAAOA,EAQjD,IANA,IAAIE,EAAO,EACVC,EAAI,EACJC,EAAM,GACNC,EAAK,GAGCF,EAAIH,EAAIM,OAAQH,IAAK,CAC3B,OAAQH,EAAIO,WAAWJ,IACtB,QACCE,EAAK,SACL,MACD,QACCA,EAAK,QACL,MACD,QACCA,EAAK,OACL,MACD,QACC,SAGEF,IAAMD,IAAME,GAAOJ,EAAIQ,MAAMN,EAAMC,IACvCC,GAAOC,EAEPH,EAAOC,EAAI,EAGZ,OADIA,IAAMD,IAAME,GAAOJ,EAAIQ,MAAMN,EAAMC,IAChCC,MAGGK,EAAS,SAACC,EAAGC,UACvBC,OAAOF,GAAGG,QAAQ,SAAU,MAAQF,GAAQ,QAElCG,EAAgB,SAACJ,EAAGJ,EAAQS,UACtCH,OAAOF,GAAGJ,QAAUA,GAAU,MAC5BS,IAA4C,IAA7BH,OAAOF,GAAGM,QAAQ,QACP,IAA5BJ,OAAOF,GAAGM,QAAQ,MAEbC,EAAY,GAEZC,EAAY,oBAEFC,EAAcT,GAC7B,IAAIV,EAAM,GACV,IAAK,IAAIoB,KAAQV,EAAG,CACnB,IAAIW,EAAMX,EAAEU,GACD,MAAPC,GAAuB,KAARA,IACdrB,IAAKA,GAAO,KAEhBA,GACY,KAAXoB,EAAK,GACFA,EACAH,EAAUG,KACTH,EAAUG,GAAQA,EAAKP,QAAQK,EAAW,OAAOI,eAGrDtB,EADkB,iBAARqB,IAAsD,IAAlC3B,EAAmBO,KAAKmB,GAChDpB,EAAM,KAAOqB,EAAM,MAEnBrB,EAAM,KAAOqB,EAAM,KAI5B,OAAOrB,QAAOuB,WAUCC,EAAYC,EAAaC,GAMxC,OALIC,MAAMC,QAAQF,GACjBA,EAASG,OAAOL,EAAaC,GACP,MAAZC,IAAiC,IAAbA,GAC9BD,EAAYK,KAAKJ,GAEXD,EAGR,SAASM,IACRC,KAAKC,KAAM,WAGIC,EAAgBC,EAAOC,GACtC,MAAO,CACNC,IAAKF,EACLC,QAAAA,EACAE,MAAOH,EAAMG,MAEbC,SAAUR,EACVS,YAAaT,EACbE,KAAK,EAELQ,IAAK,aAMSC,EAAWC,EAAUP,GACpC,IAAIQ,EAASD,EAASE,YAClBC,EAAWF,GAAUR,EAAQQ,EAAOG,KACxC,OAAiB,MAAVH,EACJE,EACCA,EAASR,MAAMU,MACfJ,EAAOK,GACRb,EC5GJ,IAAMc,EAAU,YAEAC,EACfhB,EACAC,EACAgB,EACAC,EACAC,EACAC,GAEA,GAAa,MAATpB,GAAkC,kBAAVA,EAC3B,MAAO,GAIR,GAAqB,iBAAVA,EACV,MAAqB,mBAAVA,EAA6B,GACjCpC,EAAeoC,GAGvB,IAAIqB,EAASJ,EAAKI,OACjBC,EAAaD,GAA4B,iBAAXA,EAAsBA,EAAS,KAE9D,GAAI7B,MAAMC,QAAQO,GAAQ,CAEzB,IADA,IAAIuB,EAAW,GACNvD,EAAI,EAAGA,EAAIgC,EAAM7B,OAAQH,IAC7BqD,GAAUrD,EAAI,IAAGuD,GAAsB,MAC3CA,GAECP,EACChB,EAAMhC,GACNiC,EACAgB,EACAC,EACAC,EACAC,GAGH,OAAOG,EAIR,QAA0BnC,IAAtBY,EAAMwB,YAA2B,MAAO,GAE5C,IA8SyBC,EA9SrBjB,EAAWR,EAAM0B,KACpBvB,EAAQH,EAAMG,MACdwB,GAAc,EAGf,GAAwB,mBAAbnB,EAAyB,CAEnC,GADAmB,GAAc,GACVV,EAAKW,UAAYV,IAAsC,IAA7BD,EAAKY,wBAExBrB,IAAasB,WAAU,CACjC,IAAMvC,EAAW,GAEjB,OADAF,EAAYE,EAAUS,EAAMG,MAAMZ,UAC3ByB,EACNzB,EACAU,EACAgB,GAC0B,IAA1BA,EAAKc,iBACLZ,EACAC,GAGD,IAAIG,EAEAS,EAAKhC,EAAMY,IAAMb,EAAgBC,EAAOC,GAGxCgC,UAAQC,KAAKD,UAAQC,IAAIlC,GAG7B,IAAImC,EAAaF,UAAQG,IAEzB,GACE5B,EAAS6B,WAC2B,mBAA9B7B,EAAS6B,UAAUC,OAkBpB,CACN,IAAIC,EAAOhC,EAAWC,EAAUP,IAGhC+B,EAAIhC,EAAMY,IAAM,IAAIJ,EAASL,EAAOoC,IAClCrC,IAAMF,EAERgC,EAAEQ,OAASR,EAAElC,KAAM,EACnBkC,EAAE7B,MAAQA,EACK,MAAX6B,EAAES,QAAeT,EAAES,MAAQ,IAEX,MAAhBT,EAAEU,YAA+B,MAATV,EAAEW,MAC7BX,EAAEU,WAAaV,EAAEW,IAAMX,EAAES,OAG1BT,EAAE/B,QAAUsC,EACR/B,EAASoC,yBACZZ,EAAES,MAAQI,OAAOC,OAChB,GACAd,EAAES,MACFjC,EAASoC,yBAAyBZ,EAAE7B,MAAO6B,EAAES,QAEtCT,EAAEe,qBACVf,EAAEe,qBAIFf,EAAES,MACDT,EAAEU,aAAeV,EAAES,MAChBT,EAAEU,WACFV,EAAEW,MAAQX,EAAES,MACZT,EAAEW,IACFX,EAAES,OAGHN,GAAYA,EAAWnC,GAE3BuB,EAAWS,EAAEM,OAAON,EAAE7B,MAAO6B,EAAES,MAAOT,EAAE/B,cA7CxC,IARA,IAAIsC,EAAOhC,EAAWC,EAAUP,GAO5B+C,EAAQ,EACLhB,EAAElC,KAAOkD,IAAU,IACzBhB,EAAElC,KAAM,EAEJqC,GAAYA,EAAWnC,GAG3BuB,EAAWf,EAASyC,KAAKjD,EAAMY,IAAKT,EAAOoC,GA+C7C,OALIP,EAAEkB,kBACLjD,EAAU4C,OAAOC,OAAO,GAAI7C,EAAS+B,EAAEkB,oBAGpCjB,UAAQkB,QAAQlB,UAAQkB,OAAOnD,GAC5BgB,EACNO,EACAtB,EACAgB,GAC0B,IAA1BA,EAAKc,iBACLZ,EACAC,GA9FDZ,GAsSuBiB,EAtSKjB,GAwSnB4C,aACT3B,IAAc4B,UAAY5B,EAAU6B,MAKvC,SAAkC7B,GACjC,IACC6B,GADSD,SAAShB,UAAUkB,SAASN,KAAKxB,GAC9B+B,MAAM,4BAA8B,IAAI,GACrD,IAAKF,EAAM,CAGV,IADA,IAAIG,GAAS,EACJzF,EAAI+C,EAAQ5C,OAAQH,KAC5B,GAAI+C,EAAQ/C,KAAOyD,EAAW,CAC7BgC,EAAQzF,EACR,MAIEyF,EAAQ,IACXA,EAAQ1C,EAAQpB,KAAK8B,GAAa,GAEnC6B,qBAA0BG,EAE3B,OAAOH,EAtBNI,CAAyBjC,GAtM1B,IACCkC,EACAC,EAFGrF,EAAI,IAAMiC,EAId,GAAIL,EAAO,CACV,IAAI0D,EAAQhB,OAAOiB,KAAK3D,GAGpBc,IAAgC,IAAxBA,EAAK8C,gBAAyBF,EAAMG,OAEhD,IAAK,IAAIhG,EAAI,EAAGA,EAAI6F,EAAM1F,OAAQH,IAAK,CACtC,IAAIsF,EAAOO,EAAM7F,GAChBiG,EAAI9D,EAAMmD,GACX,GAAa,aAATA,GAKJ,IAAI7F,EAAYK,KAAKwF,KAGlBrC,GAAQA,EAAKiD,eACL,QAATZ,GACS,QAATA,GACS,WAATA,GACS,aAATA,GALF,CASA,GAAa,iBAATA,EACHA,EAAO,gBACY,mBAATA,EACVA,EAAO,kBACY,oBAATA,EACVA,EAAO,mBACY,cAATA,EAAsB,CAChC,QAA2B,IAAhBnD,QAA6B,SACxCmD,EAAO,aACGnC,GAAazD,EAAMI,KAAKwF,KAClCA,EAAOA,EAAKnE,cAAcT,QAAQ,WAAY,WAG/C,GAAa,YAAT4E,EAAoB,CACvB,GAAInD,MAAW,SACfmD,EAAO,MAGK,UAATA,GAAoBW,GAAkB,iBAANA,IACnCA,EAAIjF,EAAciF,IAKH,MAAZX,EAAK,IAA4B,MAAdA,EAAK,IAA6B,kBAANW,IAClDA,EAAIxF,OAAOwF,IAGZ,IAAIE,EACHlD,EAAKmD,eACLnD,EAAKmD,cAAcd,EAAMW,EAAGhE,EAASgB,EAAMU,GAC5C,GAAIwC,GAAqB,KAAXA,EACb5F,GAAQ4F,OAIT,GAAa,4BAATb,EACHM,EAAOK,GAAKA,EAAEI,eACS,aAAb7D,GAAoC,UAAT8C,EAErCK,EAAeM,WACJA,GAAW,IAANA,GAAiB,KAANA,IAA0B,mBAANA,EAAkB,CACjE,MAAU,IAANA,GAAoB,KAANA,IACjBA,EAAIX,EAECrC,GAASA,EAAKqD,MAAK,CACvB/F,EAAIA,EAAI,IAAM+E,EACd,SAIF,GAAa,UAATA,EAAkB,CACrB,GAAiB,WAAb9C,EAAuB,CAC1BY,EAAc6C,EACd,SAGa,WAAbzD,GACAY,GAAe6C,QAEW,IAAnB9D,EAAMoE,WAEbhG,gBAGFA,EAAIA,MAAQ+E,OAAS1F,EAAeqG,cAhFpCN,EAAeM,GAsFlB,GAAI5C,EAAQ,CACX,IAAImD,EAAMjG,EAAEG,QAAQ,QAAS,KACzB8F,IAAQjG,IAAOiG,EAAI3F,QAAQ,MACtBwC,IAAW9C,EAAEM,QAAQ,QAAON,GAAQ,MADPA,EAAIiG,EAM3C,GAFAjG,GAAQ,IAEJd,EAAYK,KAAK0C,GACpB,UAAUiE,MAASjE,sCAA4CjC,GAEhE,IAKIgB,EALAmF,EACHlH,EAAcM,KAAK0C,IAClBS,EAAK0D,cAAgB1D,EAAK0D,aAAa7G,KAAK0C,GAC1CoE,EAAS,GAGb,GAAIhB,EAECvC,GAAU1C,EAAciF,KAC3BA,EAAO,KAAOtC,EAAahD,EAAOsF,EAAMtC,IAEzC/C,GAAQqF,UAEQ,MAAhBD,GACAtE,EAAaE,EAAW,GAAKoE,GAAcxF,OAC1C,CAID,IAHA,IAAI0G,EAAWxD,IAAW9C,EAAEM,QAAQ,MAChCiG,GAAc,EAET9G,EAAI,EAAGA,EAAIuB,EAASpB,OAAQH,IAAK,CACzC,IAAI+G,EAAQxF,EAASvB,GAErB,GAAa,MAAT+G,IAA2B,IAAVA,EAAiB,CACrC,IAMCC,EAAMhE,EACL+D,EACA9E,EACAgB,GACA,EATa,QAAbT,GAEgB,kBAAbA,GAEAW,EAOHC,GAMF,GAHIC,IAAWwD,GAAYlG,EAAcqG,KAAMH,GAAW,GAGtDG,EACH,GAAI3D,EAAQ,CACX,IAAI4D,EAASD,EAAI7G,OAAS,GAAe,KAAV6G,EAAI,GAI/BF,GAAeG,EAClBL,EAAOA,EAAOzG,OAAS,IAAM6G,EAE7BJ,EAAOjF,KAAKqF,GAGbF,EAAcG,OAEdL,EAAOjF,KAAKqF,IAKhB,GAAI3D,GAAUwD,EACb,IAAK,IAAI7G,EAAI4G,EAAOzG,OAAQH,KAC3B4G,EAAO5G,GAAK,KAAOsD,EAAahD,EAAOsG,EAAO5G,GAAIsD,GAKrD,GAAIsD,EAAOzG,QAAUyF,EACpBrF,GAAQqG,EAAOM,KAAK,YACVjE,GAAQA,EAAKqD,IACvB,OAAO/F,EAAE4G,UAAU,EAAG5G,EAAEJ,OAAS,GAAK,MAUvC,OAPIuG,GAAWnF,GAAaqE,GAGvBvC,IAAW9C,EAAEM,QAAQ,QAAON,GAAQ,MACxCA,EAAIA,OAASiC,OAHbjC,EAAIA,EAAEG,QAAQ,KAAM,OAMdH,MC3UF6G,EAAU,CAAExD,SAAS,GAa3ByD,EAAe/C,OAAS+C,EASpBC,IAAAA,EAAgB,SAACtF,EAAOC,UAAYoF,EAAerF,EAAOC,EAASmF,IAEjEG,EAAY,GAClB,SAASF,EAAerF,EAAOC,EAASgB,GACvChB,EAAUA,GAAW,GAOrB,IAAMuF,EAAsBvD,UAAO,IACnCA,UAAO,KAAiB,EAExB,IAGIwD,EAHEC,EAASC,IAAE7D,WAAU,MAyB3B,OAxBA4D,EAAM,IAAa,CAAC1F,GAanByF,EATAxE,IACCA,EAAKI,QACLJ,EAAK0D,cACL1D,EAAK8C,gBACL9C,EAAKW,SACLX,EAAKiD,eACLjD,EAAKqD,KACLrD,EAAKmD,eAEApD,EAAsBhB,EAAOC,EAASgB,GAEtC2E,EAAgB5F,EAAOC,GAAS,OAAOb,EAAWsG,GAKrDzD,UAAO,KAAUA,UAAO,IAASjC,EAAOuF,GAC5CtD,UAAO,IAAiBuD,EACxBD,EAAUpH,OAAS,EAEZsH,EAmFR,SAASI,EAAe7F,GACvB,OAAa,MAATA,GAAiC,kBAATA,OAGX,iBAATA,GACS,iBAATA,GACS,iBAATA,EAEA2F,IAAE,KAAM,KAAM3F,GAEfA,EAQR,SAAS8F,EAAkBxC,EAAMnC,GAChC,MAAa,cAATmC,EACI,QACY,YAATA,EACH,MACY,iBAATA,EACH,QACY,mBAATA,EACH,UACY,oBAATA,EACH,WACGnC,GAAazD,EAAMI,KAAKwF,GAC3BA,EAAKnE,cAAcT,QAAQ,WAAY,UAGxC4E,EAQR,SAASyC,EAAmBzC,EAAMW,GACjC,MAAa,UAATX,GAAyB,MAALW,GAA0B,iBAANA,EACpCjF,EAAciF,GACC,MAAZX,EAAK,IAA0B,MAAZA,EAAK,IAA2B,kBAANW,EAGhDxF,OAAOwF,GAGRA,EAGR,IAAMxE,EAAUD,MAAMC,QAChBqD,EAASD,OAAOC,OAWtB,SAAS8C,EAAgB5F,EAAOC,EAASkB,EAAWC,EAAasE,GAEhE,GAAa,MAAT1F,IAA2B,IAAVA,IAA4B,IAAVA,GAA6B,KAAVA,EACzD,MAAO,GAIR,GAAqB,iBAAVA,EACV,MAAqB,mBAAVA,EAA6B,GACjCpC,EAAeoC,GAIvB,GAAIP,EAAQO,GAAQ,CACnB,IAAIuB,EAAW,GACfmE,EAAM,IAAa1F,EACnB,IAAK,IAAIhC,EAAI,EAAGA,EAAIgC,EAAM7B,OAAQH,IACjCuD,GAECqE,EAAgB5F,EAAMhC,GAAIiC,EAASkB,EAAWC,EAAasE,GAE5D1F,EAAMhC,GAAK6H,EAAe7F,EAAMhC,IAEjC,OAAOuD,EAIR,QAA0BnC,IAAtBY,EAAMwB,YAA2B,MAAO,GAE5CxB,EAAK,GAAW0F,EACZzD,UAAO,KAAQA,UAAO,IAAOjC,GAEjC,IAAI0B,EAAO1B,EAAM0B,KAChBvB,EAAQH,EAAMG,MAIf,GADoC,mBAATuB,EACV,CAChB,IAAIH,EACJ,GAAIG,IAASI,WACZP,EAAWpB,EAAMZ,aACX,CAELgC,EADGG,EAAKW,WAA8C,mBAA1BX,EAAKW,UAAUC,OArJ/C,SAA8BtC,EAAOC,GACpC,IAAIO,EAAWR,EAAM0B,KACpBa,EAAOhC,EAAWC,EAAUP,GAGzB+B,EAAI,IAAIxB,EAASR,EAAMG,MAAOoC,GAClCvC,EAAK,IAAcgC,EACnBA,EAAC,IAAUhC,EAEXgC,EAAC,KAAU,EACXA,EAAE7B,MAAQH,EAAMG,MACD,MAAX6B,EAAES,QAAeT,EAAES,MAAQ,IAEV,MAAjBT,EAAC,MACJA,EAAC,IAAeA,EAAES,OAGnBT,EAAE/B,QAAUsC,EACR/B,EAASoC,yBACZZ,EAAES,MAAQK,EACT,GACAd,EAAES,MACFjC,EAASoC,yBAAyBZ,EAAE7B,MAAO6B,EAAES,QAEpCT,EAAEe,qBACZf,EAAEe,qBAIFf,EAAES,MAAQT,EAAC,MAAiBA,EAAES,MAAQT,EAAC,IAAeA,EAAES,OAGzD,IAAIN,EAAaF,UAAO,IAGxB,OAFIE,GAAYA,EAAWnC,GAEpBgC,EAAEM,OAAON,EAAE7B,MAAO6B,EAAES,MAAOT,EAAE/B,SAmHtB+F,CAAqBhG,EAAOC,GAvL3C,SAAiCD,EAAOC,GAGvC,IAAIsB,EACHS,EAAIjC,EAAgBC,EAAOC,GAC3BsC,EAAOhC,EAAWP,EAAM0B,KAAMzB,GAE/BD,EAAK,IAAcgC,EASnB,IAFA,IAAIG,EAAaF,UAAO,IACpBe,EAAQ,EACLhB,EAAC,KAAWgB,IAAU,IAC5BhB,EAAC,KAAU,EAEPG,GAAYA,EAAWnC,GAG3BuB,EAAWvB,EAAM0B,KAAKuB,KAAKjB,EAAGhC,EAAMG,MAAOoC,GAG5C,OAAOhB,EAgKO0E,CAAwBjG,EAAOC,GAG3C,IAAIwB,EAAYzB,EAAK,IACjByB,EAAUyB,kBACbjD,EAAU6C,EAAO,GAAI7C,EAASwB,EAAUyB,oBAW1C,IAAMrF,EAAM+H,EAHZrE,EADa,MAAZA,GAAoBA,EAASG,OAASI,YAA4B,MAAhBP,EAAS2E,IAC5B3E,EAASpB,MAAMZ,SAAWgC,EAKzDtB,EACAkB,EACAC,EACApB,GAQD,OALIiC,UAAO,QAAUA,UAAO,OAASjC,GACrCA,EAAK,QAAWZ,EAEZ6C,UAAQkE,SAASlE,UAAQkE,QAAQnG,GAE9BnC,EAIR,IACC0B,EACAqE,EAFGrF,EAAI,IAMR,GAFAA,GAAQmD,EAEJvB,EAEH,IAAK,IAAImD,KADT/D,EAAWY,EAAMZ,SACAY,EAAO,CACvB,IAAI8D,EAAI9D,EAAMmD,GAEd,KACU,QAATA,GACS,QAATA,GACS,WAATA,GACS,aAATA,GACS,aAATA,GACU,cAATA,GAAwB,UAAWnD,GAC1B,YAATmD,GAAsB,QAASnD,GAK7B1C,EAAYK,KAAKwF,IAKrB,GAFAW,EAAI8B,EADJzC,EAAOwC,EAAkBxC,EAAMnC,GACF8C,GAEhB,4BAATX,EACHM,EAAOK,GAAKA,EAAEI,eACK,aAAT3C,GAAgC,UAAT4B,EAEjC/D,EAAW0E,WACAA,GAAW,IAANA,GAAiB,KAANA,IAA0B,mBAANA,EAAkB,CACjE,IAAU,IAANA,GAAoB,KAANA,EAAU,CAC3BA,EAAIX,EACJ/E,EAAIA,EAAI,IAAM+E,EACd,SAGD,GAAa,UAATA,EAAkB,CACrB,GAAa,WAAT5B,EAAmB,CACtBN,EAAc6C,EACd,SAGS,WAATvC,GACAN,GAAe6C,GAEb,aAAc9D,IAEhB5B,GAAQ,aAGVA,EAAIA,EAAI,IAAM+E,EAAO,KAAO1F,EAAeqG,GAAK,KAKnD,IAAImC,EAAe7H,EAGnB,GAFAA,GAAQ,IAEJd,EAAYK,KAAK4D,GACpB,UAAU+C,MAAS/C,sCAAwCnD,GAG5D,IAAIqG,EAAS,GACTyB,GAAc,EAElB,GAAIzC,EACHgB,GAAkBhB,EAClByC,GAAc,UACgB,iBAAb9G,EACjBqF,GAAkBhH,EAAe2B,GACjC8G,GAAc,UACJ5G,EAAQF,GAAW,CAC7BS,EAAK,IAAaT,EAClB,IAAK,IAAIvB,EAAI,EAAGA,EAAIuB,EAASpB,OAAQH,IAAK,CACzC,IAAI+G,EAAQxF,EAASvB,GAGrB,GAFAuB,EAASvB,GAAK6H,EAAed,GAEhB,MAATA,IAA2B,IAAVA,EAAiB,CACrC,IAEIC,EAAMY,EACTb,EACA9E,EAHS,QAATyB,GAA4B,kBAATA,GAA4BP,EAK/CC,EACApB,GAIGgF,IACHJ,GAAkBI,EAClBqB,GAAc,aAIK,MAAZ9G,IAAiC,IAAbA,IAAmC,IAAbA,EAAmB,CACvES,EAAK,IAAa,CAAC6F,EAAetG,IAClC,IAEIyF,EAAMY,EACTrG,EACAU,EAHS,QAATyB,GAA4B,kBAATA,GAA4BP,EAK/CC,EACApB,GAIGgF,IACHJ,GAAkBI,EAClBqB,GAAc,GAQhB,GAJIpE,UAAO,QAAUA,UAAO,OAASjC,GACrCA,EAAK,QAAWZ,EACZ6C,UAAQkE,SAASlE,UAAQkE,QAAQnG,GAEjCqG,EACH9H,GAAQqG,UACEpH,EAAcM,KAAK4D,GAC7B,OAAO0E,EAAe,MAGvB,OAAO7H,EAAI,KAAOmD,EAAO,IAK1B2D,EAAeC,cAAgBA"}