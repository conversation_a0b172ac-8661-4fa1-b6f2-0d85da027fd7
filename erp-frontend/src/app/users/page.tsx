"use client"

import { MainLayout } from "@/components/layout/main-layout"
import { ProtectedRoute, PermissionGuard } from "@/components/auth/protected-route"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DataTable, Column } from "@/components/ui/data-table"
import { Button } from "@/components/ui/button"
import { formatDate } from "@/lib/utils"
import { getAllUsers, User } from "@/lib/auth"
import { Plus, Shield, Users, UserCheck, UserX, Edit, Trash2 } from "lucide-react"

export default function UsersPage() {
  const users = getAllUsers()

  const userColumns: Column<User>[] = [
    {
      key: "name",
      title: "الاسم",
      sortable: true,
      render: (value, row) => (
        <div className="flex items-center gap-3">
          <div className="p-2 bg-muted rounded-full">
            <Shield className="h-4 w-4" />
          </div>
          <div>
            <div className="font-medium">{value}</div>
            <div className="text-sm text-muted-foreground">{row.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: "role",
      title: "الدور",
      sortable: true,
      render: (value) => {
        const roleColors = {
          admin: "bg-red-100 text-red-800",
          manager: "bg-blue-100 text-blue-800",
          employee: "bg-green-100 text-green-800",
          viewer: "bg-purple-100 text-purple-800",
        }
        const roleNames = {
          admin: "مدير النظام",
          manager: "مدير",
          employee: "موظف",
          viewer: "مراجع",
        }
        return (
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${roleColors[value as keyof typeof roleColors]}`}>
            {roleNames[value as keyof typeof roleNames]}
          </span>
        )
      },
      width: "120px",
    },
    {
      key: "department",
      title: "القسم",
      width: "120px",
    },
    {
      key: "permissions",
      title: "الصلاحيات",
      render: (value: any[]) => (
        <div className="max-w-xs">
          <div className="text-sm">
            {value.slice(0, 3).map((perm, index) => (
              <div key={index} className="truncate">
                {perm.module}: {perm.actions.length} صلاحية
              </div>
            ))}
            {value.length > 3 && (
              <div className="text-muted-foreground">
                +{value.length - 3} صلاحية أخرى
              </div>
            )}
          </div>
        </div>
      ),
      width: "200px",
    },
    {
      key: "isActive",
      title: "الحالة",
      render: (value) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          value ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
        }`}>
          {value ? "نشط" : "معطل"}
        </span>
      ),
      width: "100px",
    },
    {
      key: "lastLogin",
      title: "آخر دخول",
      sortable: true,
      render: (value) => value ? formatDate(value) : "لم يسجل دخول",
      width: "120px",
    },
    {
      key: "createdAt",
      title: "تاريخ الإنشاء",
      sortable: true,
      render: (value) => formatDate(value),
      width: "120px",
    },
    {
      key: "id",
      title: "الإجراءات",
      render: (value, row) => (
        <PermissionGuard module="users" action="write">
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm">
              <Edit className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </PermissionGuard>
      ),
      width: "120px",
    },
  ]

  const activeUsers = users.filter(u => u.isActive)
  const adminUsers = users.filter(u => u.role === 'admin')
  const managerUsers = users.filter(u => u.role === 'manager')
  const employeeUsers = users.filter(u => u.role === 'employee')

  return (
    <ProtectedRoute module="users" action="read">
      <MainLayout>
        <div className="space-y-6">
          {/* Page Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold">إدارة المستخدمين</h1>
              <p className="text-muted-foreground">
                إدارة حسابات المستخدمين والصلاحيات
              </p>
            </div>
            <PermissionGuard module="users" action="write">
              <Button>
                <Plus className="ml-2 h-4 w-4" />
                إضافة مستخدم جديد
              </Button>
            </PermissionGuard>
          </div>

          {/* Stats Cards */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  إجمالي المستخدمين
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{users.length}</div>
                <p className="text-xs text-muted-foreground">
                  مستخدم مسجل
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  المستخدمين النشطين
                </CardTitle>
                <UserCheck className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{activeUsers.length}</div>
                <p className="text-xs text-muted-foreground">
                  {Math.round((activeUsers.length / users.length) * 100)}% من إجمالي المستخدمين
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  المديرين
                </CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{adminUsers.length + managerUsers.length}</div>
                <p className="text-xs text-muted-foreground">
                  مدير ومدير نظام
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  الموظفين
                </CardTitle>
                <UserCheck className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{employeeUsers.length}</div>
                <p className="text-xs text-muted-foreground">
                  موظف وعضو
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Users Table */}
          <Card>
            <CardHeader>
              <CardTitle>قائمة المستخدمين</CardTitle>
              <CardDescription>
                جميع المستخدمين المسجلين في النظام
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable
                data={users}
                columns={userColumns}
                pageSize={10}
                mobileCardRender={(user) => (
                  <div className="p-4 space-y-3">
                    <div className="flex justify-between items-start">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-muted rounded-full">
                          <Shield className="h-4 w-4" />
                        </div>
                        <div>
                          <div className="font-medium">{user.name}</div>
                          <div className="text-sm text-muted-foreground">{user.email}</div>
                        </div>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        user.isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                      }`}>
                        {user.isActive ? "نشط" : "معطل"}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="text-muted-foreground">الدور: </span>
                        <span>{user.role}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">القسم: </span>
                        <span>{user.department}</span>
                      </div>
                    </div>

                    <div className="text-sm">
                      <div className="text-muted-foreground mb-1">الصلاحيات:</div>
                      <div className="space-y-1">
                        {user.permissions.slice(0, 3).map((perm, index) => (
                          <div key={index} className="text-xs bg-muted px-2 py-1 rounded">
                            {perm.module}: {perm.actions.join(', ')}
                          </div>
                        ))}
                        {user.permissions.length > 3 && (
                          <div className="text-xs text-muted-foreground">
                            +{user.permissions.length - 3} صلاحية أخرى
                          </div>
                        )}
                      </div>
                    </div>

                    <PermissionGuard module="users" action="write">
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm" className="flex-1">
                          <Edit className="ml-2 h-4 w-4" />
                          تعديل
                        </Button>
                        <Button variant="outline" size="sm" className="flex-1">
                          <Trash2 className="ml-2 h-4 w-4" />
                          حذف
                        </Button>
                      </div>
                    </PermissionGuard>
                  </div>
                )}
              />
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
