/** @type {import(".").OAuthProvider} */
export default function OneLogin(options) {
  return {
    id: "onelogin",
    name: "OneLog<PERSON>",
    type: "oauth",
    wellKnown: `${options.issuer}/oidc/2/.well-known/openid-configuration`,
    authorization: { params: { scope: "openid profile email" } },
    idToken: true,
    profile(profile) {
      return {
        id: profile.sub,
        name: profile.nickname,
        email: profile.email,
        image: profile.picture,
      }
    },
    options,
  }
}
