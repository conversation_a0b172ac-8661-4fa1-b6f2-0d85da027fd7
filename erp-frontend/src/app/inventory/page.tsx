"use client"

import { MainLayout } from "@/components/layout/main-layout"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DataTable, Column } from "@/components/ui/data-table"
import { But<PERSON> } from "@/components/ui/button"
import { formatCurrency } from "@/lib/utils"
import { Plus, Package, AlertTriangle, TrendingUp, TrendingDown } from "lucide-react"

// Sample inventory data
const inventory = [
  {
    id: "PROD-001",
    name: "لابتوب ديل XPS 13",
    category: "أجهزة كمبيوتر",
    sku: "DELL-XPS13-001",
    quantity: 25,
    minStock: 10,
    maxStock: 50,
    unitPrice: 25000.00,
    totalValue: 625000.00,
    supplier: "شركة التقنية المتقدمة",
    location: "مخزن أ - رف 1",
    status: "متوفر",
    lastUpdated: "2024-01-15",
  },
  {
    id: "PROD-002",
    name: "طابعة HP LaserJet",
    category: "طابعات",
    sku: "HP-LJ-002",
    quantity: 5,
    minStock: 8,
    maxStock: 20,
    unitPrice: 3500.00,
    totalValue: 17500.00,
    supplier: "مؤسسة الطباعة الحديثة",
    location: "مخزن ب - رف 3",
    status: "مخزون منخفض",
    lastUpdated: "2024-01-14",
  },
  {
    id: "PROD-003",
    name: "شاشة سامسونج 27 بوصة",
    category: "شاشات",
    sku: "SAM-MON27-003",
    quantity: 15,
    minStock: 5,
    maxStock: 30,
    unitPrice: 4200.00,
    totalValue: 63000.00,
    supplier: "شركة العرض المرئي",
    location: "مخزن أ - رف 2",
    status: "متوفر",
    lastUpdated: "2024-01-13",
  },
  {
    id: "PROD-004",
    name: "كيبورد لوجيتك ميكانيكي",
    category: "ملحقات",
    sku: "LOG-KB-004",
    quantity: 0,
    minStock: 10,
    maxStock: 40,
    unitPrice: 850.00,
    totalValue: 0.00,
    supplier: "متجر الملحقات",
    location: "مخزن ج - رف 1",
    status: "نفد المخزون",
    lastUpdated: "2024-01-12",
  },
  {
    id: "PROD-005",
    name: "ماوس لاسلكي",
    category: "ملحقات",
    sku: "WIRE-MOUSE-005",
    quantity: 35,
    minStock: 15,
    maxStock: 60,
    unitPrice: 320.00,
    totalValue: 11200.00,
    supplier: "متجر الملحقات",
    location: "مخزن ج - رف 2",
    status: "متوفر",
    lastUpdated: "2024-01-11",
  },
  {
    id: "PROD-006",
    name: "هارد ديسك خارجي 1TB",
    category: "تخزين",
    sku: "EXT-HDD-1TB-006",
    quantity: 12,
    minStock: 8,
    maxStock: 25,
    unitPrice: 1200.00,
    totalValue: 14400.00,
    supplier: "شركة التخزين الرقمي",
    location: "مخزن ب - رف 1",
    status: "متوفر",
    lastUpdated: "2024-01-10",
  },
]

const inventoryColumns: Column<typeof inventory[0]>[] = [
  {
    key: "sku",
    title: "رمز المنتج",
    sortable: true,
    width: "120px",
  },
  {
    key: "name",
    title: "اسم المنتج",
    sortable: true,
    render: (value, row) => (
      <div>
        <div className="font-medium">{value}</div>
        <div className="text-sm text-muted-foreground">{row.category}</div>
      </div>
    ),
  },
  {
    key: "quantity",
    title: "الكمية",
    sortable: true,
    render: (value, row) => (
      <div className="flex items-center">
        <span className={`font-medium ${
          value === 0 ? 'text-red-600' :
          value <= row.minStock ? 'text-yellow-600' :
          'text-green-600'
        }`}>
          {value}
        </span>
        {value <= row.minStock && value > 0 && (
          <AlertTriangle className="h-4 w-4 text-yellow-500 mr-1" />
        )}
        {value === 0 && (
          <AlertTriangle className="h-4 w-4 text-red-500 mr-1" />
        )}
      </div>
    ),
    width: "100px",
  },
  {
    key: "minStock",
    title: "الحد الأدنى",
    sortable: true,
    width: "100px",
  },
  {
    key: "unitPrice",
    title: "سعر الوحدة",
    sortable: true,
    render: (value) => formatCurrency(value),
    width: "120px",
  },
  {
    key: "totalValue",
    title: "القيمة الإجمالية",
    sortable: true,
    render: (value) => formatCurrency(value),
    width: "140px",
  },
  {
    key: "supplier",
    title: "المورد",
    width: "150px",
  },
  {
    key: "location",
    title: "الموقع",
    width: "120px",
  },
  {
    key: "status",
    title: "الحالة",
    render: (value) => (
      <span
        className={`px-2 py-1 rounded-full text-xs font-medium ${
          value === "متوفر"
            ? "bg-green-100 text-green-800"
            : value === "مخزون منخفض"
            ? "bg-yellow-100 text-yellow-800"
            : "bg-red-100 text-red-800"
        }`}
      >
        {value}
      </span>
    ),
    width: "120px",
  },
]

export default function InventoryPage() {
  const totalValue = inventory.reduce((sum, item) => sum + item.totalValue, 0)
  const lowStockItems = inventory.filter(item => item.quantity <= item.minStock && item.quantity > 0)
  const outOfStockItems = inventory.filter(item => item.quantity === 0)
  const totalItems = inventory.reduce((sum, item) => sum + item.quantity, 0)

  return (
    <ProtectedRoute module="inventory" action="read">
      <MainLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">إدارة المخزون</h1>
            <p className="text-muted-foreground">
              متابعة وإدارة مخزون المنتجات
            </p>
          </div>
          <Button>
            <Plus className="ml-2 h-4 w-4" />
            إضافة منتج جديد
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                إجمالي المنتجات
              </CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{inventory.length}</div>
              <p className="text-xs text-muted-foreground">
                منتج مختلف
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                إجمالي الكمية
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalItems}</div>
              <p className="text-xs text-muted-foreground">
                قطعة في المخزون
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                قيمة المخزون
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(totalValue)}</div>
              <p className="text-xs text-muted-foreground">
                القيمة الإجمالية
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                تنبيهات المخزون
              </CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {outOfStockItems.length + lowStockItems.length}
              </div>
              <p className="text-xs text-muted-foreground">
                منتج يحتاج انتباه
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Alerts */}
        {(lowStockItems.length > 0 || outOfStockItems.length > 0) && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 text-yellow-500 ml-2" />
                تنبيهات المخزون
              </CardTitle>
              <CardDescription>
                منتجات تحتاج إعادة تموين
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {outOfStockItems.map((item) => (
                  <div
                    key={item.id}
                    className="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg"
                  >
                    <div>
                      <span className="font-medium text-red-800">{item.name}</span>
                      <span className="text-sm text-red-600 mr-2">- نفد المخزون</span>
                    </div>
                    <Button variant="outline" size="sm">
                      طلب تموين
                    </Button>
                  </div>
                ))}
                {lowStockItems.map((item) => (
                  <div
                    key={item.id}
                    className="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg"
                  >
                    <div>
                      <span className="font-medium text-yellow-800">{item.name}</span>
                      <span className="text-sm text-yellow-600 mr-2">
                        - متبقي {item.quantity} (الحد الأدنى: {item.minStock})
                      </span>
                    </div>
                    <Button variant="outline" size="sm">
                      طلب تموين
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Inventory Table */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة المخزون</CardTitle>
            <CardDescription>
              جميع المنتجات الموجودة في المخزون
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DataTable
              data={inventory}
              columns={inventoryColumns}
              pageSize={10}
              mobileCardRender={(item) => (
                <div className="p-4 space-y-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="font-medium">{item.name}</div>
                      <div className="text-sm text-muted-foreground">{item.category}</div>
                      <div className="text-xs text-muted-foreground">SKU: {item.sku}</div>
                    </div>
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        item.status === "متوفر"
                          ? "bg-green-100 text-green-800"
                          : item.status === "مخزون منخفض"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {item.status}
                    </span>
                  </div>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-muted-foreground">الكمية: </span>
                      <span className={`font-medium ${
                        item.quantity === 0 ? 'text-red-600' :
                        item.quantity <= item.minStock ? 'text-yellow-600' :
                        'text-green-600'
                      }`}>
                        {item.quantity}
                      </span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">الحد الأدنى: </span>
                      <span>{item.minStock}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">سعر الوحدة: </span>
                      <span>{formatCurrency(item.unitPrice)}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">القيمة: </span>
                      <span>{formatCurrency(item.totalValue)}</span>
                    </div>
                  </div>
                  <div className="text-sm">
                    <div className="text-muted-foreground">المورد: {item.supplier}</div>
                    <div className="text-muted-foreground">الموقع: {item.location}</div>
                  </div>
                </div>
              )}
            />
          </CardContent>
        </Card>
      </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
