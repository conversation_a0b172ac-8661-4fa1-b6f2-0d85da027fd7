"use client"

import { MainLayout } from "@/components/layout/main-layout"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DataTable, Column } from "@/components/ui/data-table"
import { But<PERSON> } from "@/components/ui/button"
import { formatCurrency, formatDate } from "@/lib/utils"
import { Plus, ShoppingCart, TrendingUp, DollarSign, Users, Eye, Edit, Trash2 } from "lucide-react"

// Sample sales data
const sales = [
  {
    id: "SALE-001",
    invoiceNumber: "INV-2024-001",
    customer: "أحمد محمد علي",
    customerEmail: "<EMAIL>",
    items: [
      { name: "لابتوب ديل XPS 13", quantity: 1, price: 25000.00 },
      { name: "ماوس لاسلكي", quantity: 2, price: 320.00 }
    ],
    subtotal: 25640.00,
    tax: 3846.00,
    discount: 500.00,
    total: 28986.00,
    paymentMethod: "نقدي",
    paymentStatus: "مدفوع",
    status: "مكتمل",
    salesPerson: "محمد أحمد",
    date: "2024-01-15",
    dueDate: "2024-01-15",
  },
  {
    id: "SALE-002",
    invoiceNumber: "INV-2024-002",
    customer: "فاطمة علي حسن",
    customerEmail: "<EMAIL>",
    items: [
      { name: "طابعة HP LaserJet", quantity: 1, price: 3500.00 },
      { name: "شاشة سامسونج 27 بوصة", quantity: 1, price: 4200.00 }
    ],
    subtotal: 7700.00,
    tax: 1155.00,
    discount: 0.00,
    total: 8855.00,
    paymentMethod: "بطاقة ائتمان",
    paymentStatus: "مدفوع",
    status: "مكتمل",
    salesPerson: "سارة محمود",
    date: "2024-01-14",
    dueDate: "2024-01-14",
  },
  {
    id: "SALE-003",
    invoiceNumber: "INV-2024-003",
    customer: "محمد حسن إبراهيم",
    customerEmail: "<EMAIL>",
    items: [
      { name: "هارد ديسك خارجي 1TB", quantity: 3, price: 1200.00 },
      { name: "كيبورد لوجيتك ميكانيكي", quantity: 2, price: 850.00 }
    ],
    subtotal: 5300.00,
    tax: 795.00,
    discount: 200.00,
    total: 5895.00,
    paymentMethod: "تحويل بنكي",
    paymentStatus: "قيد الانتظار",
    status: "قيد المعالجة",
    salesPerson: "أحمد علي",
    date: "2024-01-13",
    dueDate: "2024-01-20",
  },
  {
    id: "SALE-004",
    invoiceNumber: "INV-2024-004",
    customer: "سارة أحمد محمود",
    customerEmail: "<EMAIL>",
    items: [
      { name: "ماوس لاسلكي", quantity: 5, price: 320.00 }
    ],
    subtotal: 1600.00,
    tax: 240.00,
    discount: 50.00,
    total: 1790.00,
    paymentMethod: "نقدي",
    paymentStatus: "مدفوع",
    status: "مكتمل",
    salesPerson: "نورا سامي",
    date: "2024-01-12",
    dueDate: "2024-01-12",
  },
  {
    id: "SALE-005",
    invoiceNumber: "INV-2024-005",
    customer: "عمر خالد يوسف",
    customerEmail: "<EMAIL>",
    items: [
      { name: "لابتوب ديل XPS 13", quantity: 2, price: 25000.00 },
      { name: "شاشة سامسونج 27 بوصة", quantity: 2, price: 4200.00 }
    ],
    subtotal: 58400.00,
    tax: 8760.00,
    discount: 1000.00,
    total: 66160.00,
    paymentMethod: "شيك",
    paymentStatus: "غير مدفوع",
    status: "ملغي",
    salesPerson: "حسام محمد",
    date: "2024-01-11",
    dueDate: "2024-01-25",
  },
]

const salesColumns: Column<typeof sales[0]>[] = [
  {
    key: "invoiceNumber",
    title: "رقم الفاتورة",
    sortable: true,
    width: "130px",
  },
  {
    key: "customer",
    title: "العميل",
    sortable: true,
    render: (value, row) => (
      <div>
        <div className="font-medium">{value}</div>
        <div className="text-sm text-muted-foreground">{row.customerEmail}</div>
      </div>
    ),
  },
  {
    key: "items",
    title: "المنتجات",
    render: (value) => (
      <div className="max-w-xs">
        <div className="text-sm">
          {value.slice(0, 2).map((item: any, index: number) => (
            <div key={index} className="truncate">
              {item.quantity}x {item.name}
            </div>
          ))}
          {value.length > 2 && (
            <div className="text-muted-foreground">
              +{value.length - 2} منتج آخر
            </div>
          )}
        </div>
      </div>
    ),
    width: "200px",
  },
  {
    key: "total",
    title: "المبلغ الإجمالي",
    sortable: true,
    render: (value) => formatCurrency(value),
    width: "130px",
  },
  {
    key: "paymentMethod",
    title: "طريقة الدفع",
    width: "120px",
  },
  {
    key: "paymentStatus",
    title: "حالة الدفع",
    render: (value) => (
      <span
        className={`px-2 py-1 rounded-full text-xs font-medium ${
          value === "مدفوع"
            ? "bg-green-100 text-green-800"
            : value === "قيد الانتظار"
            ? "bg-yellow-100 text-yellow-800"
            : "bg-red-100 text-red-800"
        }`}
      >
        {value}
      </span>
    ),
    width: "120px",
  },
  {
    key: "status",
    title: "الحالة",
    render: (value) => (
      <span
        className={`px-2 py-1 rounded-full text-xs font-medium ${
          value === "مكتمل"
            ? "bg-green-100 text-green-800"
            : value === "قيد المعالجة"
            ? "bg-blue-100 text-blue-800"
            : "bg-red-100 text-red-800"
        }`}
      >
        {value}
      </span>
    ),
    width: "120px",
  },
  {
    key: "salesPerson",
    title: "مندوب المبيعات",
    width: "130px",
  },
  {
    key: "date",
    title: "التاريخ",
    sortable: true,
    render: (value) => formatDate(value),
    width: "120px",
  },
  {
    key: "id",
    title: "الإجراءات",
    render: (value, row) => (
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="sm">
          <Eye className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm">
          <Edit className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm">
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    ),
    width: "120px",
  },
]

export default function SalesPage() {
  const totalSales = sales.reduce((sum, sale) => sum + sale.total, 0)
  const completedSales = sales.filter(sale => sale.status === "مكتمل")
  const pendingSales = sales.filter(sale => sale.status === "قيد المعالجة")
  const paidSales = sales.filter(sale => sale.paymentStatus === "مدفوع")

  return (
    <ProtectedRoute module="sales" action="read">
      <MainLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">إدارة المبيعات</h1>
            <p className="text-muted-foreground">
              متابعة وإدارة عمليات البيع والفواتير
            </p>
          </div>
          <Button>
            <Plus className="ml-2 h-4 w-4" />
            إنشاء فاتورة جديدة
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                إجمالي المبيعات
              </CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(totalSales)}</div>
              <p className="text-xs text-muted-foreground">
                +12.5% من الشهر الماضي
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                عدد الفواتير
              </CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{sales.length}</div>
              <p className="text-xs text-muted-foreground">
                فاتورة هذا الشهر
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                المبيعات المكتملة
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{completedSales.length}</div>
              <p className="text-xs text-muted-foreground">
                {Math.round((completedSales.length / sales.length) * 100)}% من إجمالي الفواتير
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                المدفوعات المحصلة
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(paidSales.reduce((sum, sale) => sum + sale.total, 0))}
              </div>
              <p className="text-xs text-muted-foreground">
                {paidSales.length} فاتورة مدفوعة
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Pending Sales Alert */}
        {pendingSales.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ShoppingCart className="h-5 w-5 text-blue-500 ml-2" />
                مبيعات قيد المعالجة
              </CardTitle>
              <CardDescription>
                فواتير تحتاج متابعة
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {pendingSales.map((sale) => (
                  <div
                    key={sale.id}
                    className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg"
                  >
                    <div>
                      <span className="font-medium text-blue-800">{sale.invoiceNumber}</span>
                      <span className="text-sm text-blue-600 mr-2">- {sale.customer}</span>
                      <span className="text-sm text-blue-600">({formatCurrency(sale.total)})</span>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        عرض التفاصيل
                      </Button>
                      <Button size="sm">
                        تحديث الحالة
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Sales Table */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة المبيعات</CardTitle>
            <CardDescription>
              جميع فواتير المبيعات
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DataTable
              data={sales}
              columns={salesColumns}
              pageSize={10}
              mobileCardRender={(sale) => (
                <div className="p-4 space-y-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="font-medium">{sale.invoiceNumber}</div>
                      <div className="text-sm text-muted-foreground">{sale.customer}</div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">{formatCurrency(sale.total)}</div>
                      <div className="text-sm text-muted-foreground">{formatDate(sale.date)}</div>
                    </div>
                  </div>

                  <div className="flex justify-between">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        sale.paymentStatus === "مدفوع"
                          ? "bg-green-100 text-green-800"
                          : sale.paymentStatus === "قيد الانتظار"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {sale.paymentStatus}
                    </span>
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        sale.status === "مكتمل"
                          ? "bg-green-100 text-green-800"
                          : sale.status === "قيد المعالجة"
                          ? "bg-blue-100 text-blue-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {sale.status}
                    </span>
                  </div>

                  <div className="text-sm space-y-1">
                    <div>طريقة الدفع: {sale.paymentMethod}</div>
                    <div>مندوب المبيعات: {sale.salesPerson}</div>
                    <div>عدد المنتجات: {sale.items.length}</div>
                  </div>

                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Eye className="ml-2 h-4 w-4" />
                      عرض
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <Edit className="ml-2 h-4 w-4" />
                      تعديل
                    </Button>
                  </div>
                </div>
              )}
            />
          </CardContent>
        </Card>
      </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
